const jwt = require('jsonwebtoken');
const logger = require('./logger');

/**
 * JWT工具类
 * 处理JWT token的生成、验证和管理
 */
class JWTUtils {
  constructor() {
    this.accessSecret = process.env.JWT_ACCESS_SECRET;
    this.refreshSecret = process.env.JWT_REFRESH_SECRET;
    this.accessExpiresIn = process.env.JWT_ACCESS_EXPIRES_IN || '15m';
    this.refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
    
    if (!this.accessSecret || !this.refreshSecret) {
      throw new Error('JWT secrets are not configured');
    }
  }

  /**
   * 生成访问令牌
   * @param {Object} payload - 用户信息
   * @returns {string} JWT token
   */
  generateAccessToken(payload) {
    try {
      const tokenPayload = {
        userId: payload.id,
        studentId: payload.studentId,
        role: payload.role,
        language: payload.language || 'zh-cn',
        permissions: this.getUserPermissions(payload.role),
        type: 'access'
      };

      const token = jwt.sign(tokenPayload, this.accessSecret, {
        expiresIn: this.accessExpiresIn,
        issuer: 'todo-system',
        audience: 'todo-system-users',
        algorithm: 'HS256'
      });

      logger.audit('Access token generated', {
        userId: payload.id,
        studentId: payload.studentId,
        role: payload.role
      });

      return token;
    } catch (error) {
      logger.error('Failed to generate access token', {
        error: error.message,
        userId: payload.id
      });
      throw new Error('Token generation failed');
    }
  }

  /**
   * 生成刷新令牌
   * @param {Object} payload - 用户信息
   * @returns {string} JWT refresh token
   */
  generateRefreshToken(payload) {
    try {
      const tokenPayload = {
        userId: payload.id,
        studentId: payload.studentId,
        role: payload.role,
        type: 'refresh'
      };

      const token = jwt.sign(tokenPayload, this.refreshSecret, {
        expiresIn: this.refreshExpiresIn,
        issuer: 'todo-system',
        audience: 'todo-system-users',
        algorithm: 'HS256'
      });

      logger.audit('Refresh token generated', {
        userId: payload.id,
        studentId: payload.studentId
      });

      return token;
    } catch (error) {
      logger.error('Failed to generate refresh token', {
        error: error.message,
        userId: payload.id
      });
      throw new Error('Refresh token generation failed');
    }
  }

  /**
   * 验证访问令牌
   * @param {string} token - JWT token
   * @returns {Object} 解码后的payload
   */
  verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(token, this.accessSecret, {
        issuer: 'todo-system',
        audience: 'todo-system-users',
        algorithms: ['HS256']
      });

      if (decoded.type !== 'access') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        logger.security('Access token expired', { token: this.maskToken(token) });
        throw new Error('Token expired');
      } else if (error.name === 'JsonWebTokenError') {
        logger.security('Invalid access token', { 
          token: this.maskToken(token),
          error: error.message 
        });
        throw new Error('Invalid token');
      } else {
        logger.error('Token verification failed', {
          error: error.message,
          token: this.maskToken(token)
        });
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * 验证刷新令牌
   * @param {string} token - JWT refresh token
   * @returns {Object} 解码后的payload
   */
  verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, this.refreshSecret, {
        issuer: 'todo-system',
        audience: 'todo-system-users',
        algorithms: ['HS256']
      });

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        logger.security('Refresh token expired', { token: this.maskToken(token) });
        throw new Error('Refresh token expired');
      } else if (error.name === 'JsonWebTokenError') {
        logger.security('Invalid refresh token', { 
          token: this.maskToken(token),
          error: error.message 
        });
        throw new Error('Invalid refresh token');
      } else {
        logger.error('Refresh token verification failed', {
          error: error.message,
          token: this.maskToken(token)
        });
        throw new Error('Refresh token verification failed');
      }
    }
  }

  /**
   * 解码token (不验证)
   * @param {string} token - JWT token
   * @returns {Object} 解码后的payload
   */
  decodeToken(token) {
    try {
      return jwt.decode(token);
    } catch (error) {
      logger.error('Failed to decode token', {
        error: error.message,
        token: this.maskToken(token)
      });
      return null;
    }
  }

  /**
   * 获取用户权限
   * @param {string} role - 用户角色
   * @returns {Array} 权限列表
   */
  getUserPermissions(role) {
    const rolePermissions = {
      student: [
        'todo:read',
        'todo:update',
        'attachment:upload',
        'profile:read',
        'profile:update'
      ],
      teacher: [
        'todo:read',
        'todo:create',
        'todo:update',
        'student:read',
        'template:read'
      ],
      admin: ['*'] // 所有权限
    };

    return rolePermissions[role] || [];
  }

  /**
   * 检查用户是否有特定权限
   * @param {Array} userPermissions - 用户权限列表
   * @param {string} requiredPermission - 需要的权限
   * @returns {boolean} 是否有权限
   */
  hasPermission(userPermissions, requiredPermission) {
    if (!userPermissions || !Array.isArray(userPermissions)) {
      return false;
    }

    // 检查是否有全部权限
    if (userPermissions.includes('*')) {
      return true;
    }

    // 检查具体权限
    return userPermissions.includes(requiredPermission);
  }

  /**
   * 获取token过期时间
   * @param {string} token - JWT token
   * @returns {Date|null} 过期时间
   */
  getTokenExpiration(token) {
    try {
      const decoded = this.decodeToken(token);
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      logger.error('Failed to get token expiration', {
        error: error.message,
        token: this.maskToken(token)
      });
      return null;
    }
  }

  /**
   * 检查token是否即将过期
   * @param {string} token - JWT token
   * @param {number} thresholdMinutes - 阈值分钟数
   * @returns {boolean} 是否即将过期
   */
  isTokenExpiringSoon(token, thresholdMinutes = 5) {
    try {
      const expiration = this.getTokenExpiration(token);
      if (!expiration) {
        return true;
      }

      const now = new Date();
      const threshold = new Date(now.getTime() + thresholdMinutes * 60 * 1000);
      
      return expiration <= threshold;
    } catch (error) {
      logger.error('Failed to check token expiration', {
        error: error.message,
        token: this.maskToken(token)
      });
      return true;
    }
  }

  /**
   * 掩码token用于日志记录
   * @param {string} token - JWT token
   * @returns {string} 掩码后的token
   */
  maskToken(token) {
    if (!token || typeof token !== 'string') {
      return 'invalid_token';
    }

    if (token.length <= 20) {
      return '***';
    }

    return token.substring(0, 10) + '***' + token.substring(token.length - 10);
  }

  /**
   * 生成token对 (访问令牌 + 刷新令牌)
   * @param {Object} user - 用户信息
   * @returns {Object} token对象
   */
  generateTokenPair(user) {
    try {
      const accessToken = this.generateAccessToken(user);
      const refreshToken = this.generateRefreshToken(user);

      return {
        accessToken,
        refreshToken,
        expiresIn: this.getExpiresInSeconds(this.accessExpiresIn),
        tokenType: 'Bearer'
      };
    } catch (error) {
      logger.error('Failed to generate token pair', {
        error: error.message,
        userId: user.id
      });
      throw error;
    }
  }

  /**
   * 将时间字符串转换为秒数
   * @param {string} timeString - 时间字符串 (如 '15m', '7d')
   * @returns {number} 秒数
   */
  getExpiresInSeconds(timeString) {
    const units = {
      s: 1,
      m: 60,
      h: 3600,
      d: 86400
    };

    const match = timeString.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 900; // 默认15分钟
    }

    const [, value, unit] = match;
    return parseInt(value) * (units[unit] || 1);
  }
}

// 创建单例实例
const jwtUtils = new JWTUtils();

module.exports = jwtUtils;
