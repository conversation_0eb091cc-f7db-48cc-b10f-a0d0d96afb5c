<template>
  <div class="todo-list-page">
    <!-- 头部 -->
    <div class="page-header">
      <van-nav-bar title="待办事项" fixed>
        <template #right>
          <van-icon name="plus" @click="addTodo" />
        </template>
      </van-nav-bar>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filter-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索待办事项"
        @search="handleSearch"
        @clear="handleClearSearch"
      />
      
      <div class="filter-tabs">
        <van-tabs v-model:active="activeFilter" @change="handleFilterChange">
          <van-tab title="全部" name="all" />
          <van-tab title="今日" name="today" />
          <van-tab title="本周" name="week" />
          <van-tab title="逾期" name="overdue" />
          <van-tab title="已完成" name="completed" />
        </van-tabs>
      </div>
      
      <!-- 高级筛选 -->
      <div class="advanced-filter">
        <van-button
          size="small"
          type="primary"
          plain
          @click="showFilterPopup = true"
        >
          <van-icon name="filter-o" />
          筛选
        </van-button>
        
        <van-button
          size="small"
          type="primary"
          plain
          @click="showSortPopup = true"
        >
          <van-icon name="sort" />
          排序
        </van-button>
      </div>
    </div>

    <!-- 待办列表 -->
    <div class="todo-list-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div v-if="filteredTodos.length === 0 && !loading" class="empty-state">
            <van-empty description="暂无待办事项" />
            <van-button type="primary" @click="addTodo">
              添加第一个待办
            </van-button>
          </div>
          
          <div v-else class="todo-items">
            <div
              v-for="todo in filteredTodos"
              :key="todo.id"
              class="todo-item"
              @click="goToDetail(todo.id)"
            >
              <div class="todo-checkbox">
                <van-checkbox
                  :model-value="todo.completed"
                  @click.stop="toggleTodo(todo)"
                />
              </div>
              
              <div class="todo-content">
                <div class="todo-header">
                  <h3 class="todo-title" :class="{ completed: todo.completed }">
                    {{ todo.title }}
                  </h3>
                  <div class="todo-priority" :class="todo.priority">
                    {{ getPriorityText(todo.priority) }}
                  </div>
                </div>
                
                <p v-if="todo.description" class="todo-description">
                  {{ todo.description }}
                </p>
                
                <div class="todo-meta">
                  <span class="todo-category">
                    {{ getCategoryText(todo.category) }}
                  </span>
                  
                  <span v-if="todo.dueDate" class="todo-due-date" :class="getDueDateClass(todo)">
                    <van-icon name="clock-o" />
                    {{ formatDueDate(todo.dueDate) }}
                  </span>
                  
                  <span v-if="todo.attachments?.length" class="todo-attachments">
                    <van-icon name="attachment" />
                    {{ todo.attachments.length }}
                  </span>
                </div>
                
                <!-- 进度条 -->
                <div v-if="todo.progress !== undefined" class="todo-progress">
                  <van-progress
                    :percentage="todo.progress"
                    stroke-width="4"
                    :color="getProgressColor(todo.progress)"
                  />
                </div>
              </div>
              
              <div class="todo-actions">
                <van-icon
                  name="edit"
                  @click.stop="editTodo(todo)"
                />
                <van-icon
                  name="delete-o"
                  @click.stop="deleteTodo(todo)"
                />
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup v-model:show="showFilterPopup" position="bottom" round>
      <div class="filter-popup">
        <div class="popup-header">
          <h3>筛选条件</h3>
          <van-button type="primary" size="small" @click="applyFilter">
            应用
          </van-button>
        </div>
        
        <div class="filter-options">
          <div class="filter-group">
            <h4>分类</h4>
            <van-checkbox-group v-model="filterOptions.categories">
              <van-checkbox
                v-for="category in categories"
                :key="category.value"
                :name="category.value"
              >
                {{ category.label }}
              </van-checkbox>
            </van-checkbox-group>
          </div>
          
          <div class="filter-group">
            <h4>优先级</h4>
            <van-checkbox-group v-model="filterOptions.priorities">
              <van-checkbox
                v-for="priority in priorities"
                :key="priority.value"
                :name="priority.value"
              >
                {{ priority.label }}
              </van-checkbox>
            </van-checkbox-group>
          </div>
          
          <div class="filter-group">
            <h4>状态</h4>
            <van-radio-group v-model="filterOptions.status">
              <van-radio name="all">全部</van-radio>
              <van-radio name="pending">待处理</van-radio>
              <van-radio name="completed">已完成</van-radio>
            </van-radio-group>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 排序弹窗 -->
    <van-popup v-model:show="showSortPopup" position="bottom" round>
      <div class="sort-popup">
        <div class="popup-header">
          <h3>排序方式</h3>
        </div>
        
        <van-radio-group v-model="sortOption" @change="applySorting">
          <van-cell-group>
            <van-cell
              v-for="option in sortOptions"
              :key="option.value"
              :title="option.label"
              clickable
              @click="sortOption = option.value"
            >
              <template #right-icon>
                <van-radio :name="option.value" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="addTodo"
    />

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="todo-list-o" to="/todo">待办</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useTodoStore } from '../../stores/todo'
import { useAppStore } from '../../stores/app'
import { utils } from '../../utils/global'

const router = useRouter()
const route = useRoute()
const todoStore = useTodoStore()
const appStore = useAppStore()

const { todos, loading, filteredTodos } = storeToRefs(todoStore)

// 响应式数据
const activeTab = ref(1)
const activeFilter = ref('all')
const searchKeyword = ref('')
const refreshing = ref(false)
const finished = ref(false)
const showFilterPopup = ref(false)
const showSortPopup = ref(false)
const sortOption = ref('dueDate')

const filterOptions = reactive({
  categories: [],
  priorities: [],
  status: 'all'
})

// 配置数据
const categories = [
  { value: 'academic', label: '学术相关' },
  { value: 'administrative', label: '行政手续' },
  { value: 'accommodation', label: '住宿相关' },
  { value: 'financial', label: '财务相关' },
  { value: 'health', label: '健康体检' },
  { value: 'social', label: '社交活动' },
  { value: 'other', label: '其他' }
]

const priorities = [
  { value: 'low', label: '低' },
  { value: 'medium', label: '中' },
  { value: 'high', label: '高' },
  { value: 'urgent', label: '紧急' }
]

const sortOptions = [
  { value: 'dueDate', label: '按截止日期' },
  { value: 'priority', label: '按优先级' },
  { value: 'createdAt', label: '按创建时间' },
  { value: 'title', label: '按标题' }
]

// 方法
const addTodo = () => {
  router.push('/todo/add')
}

const goToDetail = (id) => {
  router.push(`/todo/${id}`)
}

const editTodo = (todo) => {
  router.push(`/todo/${todo.id}/edit`)
}

const toggleTodo = async (todo) => {
  try {
    await todoStore.toggleTodoStatus(todo.id)
    appStore.showToast(todo.completed ? '待办已恢复' : '待办已完成', 'success')
  } catch (error) {
    console.error('切换待办状态失败:', error)
    appStore.showToast('操作失败', 'error')
  }
}

const deleteTodo = async (todo) => {
  try {
    await appStore.showDialog({
      title: '确认删除',
      message: '确定要删除这个待办事项吗？'
    })
    
    await todoStore.deleteTodo(todo.id)
    appStore.showToast('删除成功', 'success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除待办失败:', error)
      appStore.showToast('删除失败', 'error')
    }
  }
}

const handleSearch = (value) => {
  todoStore.setFilter('search', value)
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  todoStore.setFilter('search', '')
}

const handleFilterChange = (name) => {
  todoStore.setFilter('status', name)
}

const onRefresh = async () => {
  try {
    await todoStore.refreshTodos()
  } finally {
    refreshing.value = false
  }
}

const onLoad = async () => {
  try {
    await todoStore.loadMoreTodos()
    finished.value = !todoStore.pagination.hasMore
  } catch (error) {
    console.error('加载更多失败:', error)
  }
}

const applyFilter = () => {
  // 应用筛选条件
  if (filterOptions.categories.length > 0) {
    todoStore.setFilter('category', filterOptions.categories)
  }
  if (filterOptions.priorities.length > 0) {
    todoStore.setFilter('priority', filterOptions.priorities)
  }
  if (filterOptions.status !== 'all') {
    todoStore.setFilter('status', filterOptions.status)
  }
  showFilterPopup.value = false
}

const applySorting = () => {
  // 应用排序
  // 这里可以添加排序逻辑
  showSortPopup.value = false
}

const getCategoryText = (category) => {
  const item = categories.find(c => c.value === category)
  return item?.label || '其他'
}

const getPriorityText = (priority) => {
  const item = priorities.find(p => p.value === priority)
  return item?.label || '中'
}

const formatDueDate = (dueDate) => {
  if (!dueDate) return ''
  return utils.formatDate(dueDate, 'MM-DD HH:mm')
}

const getDueDateClass = (todo) => {
  if (!todo.dueDate || todo.completed) return ''
  
  const now = new Date()
  const due = new Date(todo.dueDate)
  
  if (due < now) return 'overdue'
  if (due - now < 24 * 60 * 60 * 1000) return 'urgent'
  
  return ''
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#ee0a24'
  if (progress < 70) return '#ff976a'
  return '#07c160'
}

// 监听路由查询参数
watch(() => route.query.filter, (filter) => {
  if (filter) {
    activeFilter.value = filter
    todoStore.setFilter('status', filter)
  }
}, { immediate: true })

// 生命周期
onMounted(async () => {
  try {
    await todoStore.fetchTodos(true)
    await todoStore.fetchCategories()
  } catch (error) {
    console.error('加载数据失败:', error)
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.todo-list-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: 60px;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-filter-section {
  background: white;
  padding: var(--spacing-md);
  box-shadow: var(--shadow-light);
  
  .van-search {
    margin-bottom: var(--spacing-md);
  }
  
  .filter-tabs {
    margin-bottom: var(--spacing-md);
  }
  
  .advanced-filter {
    display: flex;
    gap: var(--spacing-md);
  }
}

.todo-list-content {
  padding: var(--spacing-md);
  
  .empty-state {
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-lg);
    
    .van-button {
      margin-top: var(--spacing-lg);
    }
  }
  
  .todo-items {
    .todo-item {
      background: white;
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-md);
      box-shadow: var(--shadow-light);
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-md);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
      }
      
      .todo-checkbox {
        margin-top: 2px;
      }
      
      .todo-content {
        flex: 1;
        
        .todo-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-sm);
          
          .todo-title {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin: 0;
            flex: 1;
            
            &.completed {
              text-decoration: line-through;
              color: var(--text-tertiary);
            }
          }
          
          .todo-priority {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            margin-left: var(--spacing-sm);
            
            &.low {
              background: rgba(7, 193, 96, 0.1);
              color: var(--success-color);
            }
            
            &.medium {
              background: rgba(255, 151, 106, 0.1);
              color: var(--warning-color);
            }
            
            &.high,
            &.urgent {
              background: rgba(238, 10, 36, 0.1);
              color: var(--danger-color);
            }
          }
        }
        
        .todo-description {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          margin: 0 0 var(--spacing-sm) 0;
          @include text-ellipsis(2);
        }
        
        .todo-meta {
          display: flex;
          align-items: center;
          gap: var(--spacing-md);
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
          margin-bottom: var(--spacing-sm);
          
          .todo-category {
            background: var(--primary-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
          }
          
          .todo-due-date {
            display: flex;
            align-items: center;
            gap: 2px;
            
            &.urgent {
              color: var(--warning-color);
            }
            
            &.overdue {
              color: var(--danger-color);
            }
          }
          
          .todo-attachments {
            display: flex;
            align-items: center;
            gap: 2px;
          }
        }
        
        .todo-progress {
          margin-top: var(--spacing-sm);
        }
      }
      
      .todo-actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        
        .van-icon {
          padding: var(--spacing-xs);
          color: var(--text-tertiary);
          cursor: pointer;
          
          &:hover {
            color: var(--primary-color);
          }
        }
      }
    }
  }
}

.filter-popup,
.sort-popup {
  padding: var(--spacing-lg);
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
    }
  }
  
  .filter-options {
    .filter-group {
      margin-bottom: var(--spacing-lg);
      
      h4 {
        margin: 0 0 var(--spacing-md) 0;
        font-size: var(--font-size-md);
        font-weight: var(--font-weight-medium);
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .todo-list-content {
    padding: var(--spacing-sm);
  }
  
  .todo-items .todo-item {
    padding: var(--spacing-sm);
  }
}
</style>
