const logger = require('../utils/logger');

/**
 * 全局错误处理中间件
 * 统一处理应用中的所有错误
 */
const errorHandler = (error, req, res, next) => {
  // 记录错误日志
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    studentId: req.user?.studentId
  });

  // 默认错误响应
  let statusCode = 500;
  let errorCode = 5001;
  let message = '服务器内部错误';
  let details = null;

  // 根据错误类型设置响应
  if (error.name === 'ValidationError') {
    statusCode = 400;
    errorCode = 4010;
    message = '请求参数验证失败';
    details = error.details || error.message;
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
    errorCode = 4001;
    message = '未授权访问';
  } else if (error.name === 'ForbiddenError') {
    statusCode = 403;
    errorCode = 4003;
    message = '权限不足';
  } else if (error.name === 'NotFoundError') {
    statusCode = 404;
    errorCode = 4004;
    message = '资源不存在';
  } else if (error.name === 'ConflictError') {
    statusCode = 409;
    errorCode = 4009;
    message = '资源冲突';
  } else if (error.code === 'ECONNREFUSED') {
    statusCode = 503;
    errorCode = 5003;
    message = '外部服务不可用';
  } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
    statusCode = 503;
    errorCode = 5002;
    message = '数据库连接错误';
  } else if (error.code === 'LIMIT_FILE_SIZE') {
    statusCode = 400;
    errorCode = 4013;
    message = '文件大小超出限制';
  } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    statusCode = 400;
    errorCode = 4012;
    message = '不支持的文件类型';
  } else if (error.message && error.message.includes('duplicate')) {
    statusCode = 409;
    errorCode = 4009;
    message = '数据已存在';
  }

  // 构建错误响应
  const errorResponse = {
    success: false,
    code: errorCode,
    message,
    timestamp: new Date().toISOString()
  };

  // 在开发环境中包含更多错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error = {
      name: error.name,
      message: error.message,
      stack: error.stack
    };
    
    if (details) {
      errorResponse.details = details;
    }
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
};

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res) => {
  logger.warn('Route not found', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json({
    success: false,
    code: 4004,
    message: '请求的资源不存在',
    timestamp: new Date().toISOString()
  });
};

/**
 * 异步错误包装器
 * 用于包装异步路由处理器，自动捕获Promise rejection
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode = 500, errorCode = 5001) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 4010);
    this.name = 'ValidationError';
    this.details = details;
  }
}

class UnauthorizedError extends AppError {
  constructor(message = '未授权访问') {
    super(message, 401, 4001);
    this.name = 'UnauthorizedError';
  }
}

class ForbiddenError extends AppError {
  constructor(message = '权限不足') {
    super(message, 403, 4003);
    this.name = 'ForbiddenError';
  }
}

class NotFoundError extends AppError {
  constructor(message = '资源不存在') {
    super(message, 404, 4004);
    this.name = 'NotFoundError';
  }
}

class ConflictError extends AppError {
  constructor(message = '资源冲突') {
    super(message, 409, 4009);
    this.name = 'ConflictError';
  }
}

class DatabaseError extends AppError {
  constructor(message = '数据库操作失败') {
    super(message, 500, 5002);
    this.name = 'DatabaseError';
  }
}

class ExternalServiceError extends AppError {
  constructor(message = '外部服务不可用') {
    super(message, 503, 5003);
    this.name = 'ExternalServiceError';
  }
}

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  AppError,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  DatabaseError,
  ExternalServiceError
};
