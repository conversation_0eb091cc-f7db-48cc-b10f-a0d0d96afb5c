<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="待办事项" />
  <meta name="theme-color" content="#1989fa" />
  
  <!-- SEO -->
  <meta name="description" content="学生入学待办事项管理系统，帮助新生高效完成入学相关事务" />
  <meta name="keywords" content="入学,待办事项,学生,管理系统,新生入学" />
  <meta name="author" content="Todo System Team" />
  
  <!-- Open Graph -->
  <meta property="og:title" content="入学待办事项系统" />
  <meta property="og:description" content="学生入学待办事项管理系统" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="/icons/icon-192x192.png" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/icons/icon-152x152.png" />
  
  <!-- Preload -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <title>入学待办事项系统</title>
  
  <!-- 防止页面缩放 -->
  <script>
    document.addEventListener('touchstart', function (event) {
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    });
    
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
      const now = (new Date()).getTime();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, false);
  </script>
  
  <!-- 加载动画 -->
  <style>
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
      border-radius: 16px;
      background: linear-gradient(135deg, #1989fa 0%, #1890ff 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
      animation: pulse 2s infinite;
    }
    
    .loading-text {
      color: #666;
      font-size: 14px;
      margin-bottom: 20px;
    }
    
    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1989fa;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 加载动画 -->
    <div id="loading">
      <div class="loading-logo">📋</div>
      <div class="loading-text">入学待办事项系统</div>
      <div class="loading-spinner"></div>
    </div>
  </div>
  
  <script type="module" src="/src/main.js"></script>
  
  <!-- 隐藏加载动画 -->
  <script>
    window.addEventListener('load', function() {
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.3s ease';
          setTimeout(function() {
            loading.style.display = 'none';
          }, 300);
        }
      }, 500);
    });
  </script>
</body>
</html>
