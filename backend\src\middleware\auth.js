const jwtUtils = require('../utils/jwt');
const logger = require('../utils/logger');

/**
 * JWT认证中间件
 * 验证请求中的JWT token并设置用户信息
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '缺少认证令牌',
        timestamp: new Date().toISOString()
      });
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;

    if (!token) {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '认证令牌格式错误',
        timestamp: new Date().toISOString()
      });
    }

    // 验证token
    const decoded = jwtUtils.verifyAccessToken(token);
    
    // 设置用户信息到请求对象
    req.user = {
      id: decoded.userId,
      studentId: decoded.studentId,
      role: decoded.role,
      language: decoded.language,
      permissions: decoded.permissions || []
    };

    // 记录认证成功日志
    logger.audit('User authenticated', {
      userId: req.user.id,
      studentId: req.user.studentId,
      role: req.user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    logger.security('Authentication failed', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      authHeader: req.headers.authorization ? 'present' : 'missing'
    });

    if (error.message === 'Token expired') {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '认证令牌已过期',
        timestamp: new Date().toISOString()
      });
    } else if (error.message === 'Invalid token') {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '无效的认证令牌',
        timestamp: new Date().toISOString()
      });
    } else {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '认证失败',
        timestamp: new Date().toISOString()
      });
    }
  }
};

/**
 * 权限检查中间件工厂
 * @param {string} permission - 需要的权限
 * @returns {Function} 中间件函数
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '用户未认证',
        timestamp: new Date().toISOString()
      });
    }

    const hasPermission = jwtUtils.hasPermission(req.user.permissions, permission);
    
    if (!hasPermission) {
      logger.security('Permission denied', {
        userId: req.user.id,
        studentId: req.user.studentId,
        role: req.user.role,
        requiredPermission: permission,
        userPermissions: req.user.permissions,
        ip: req.ip,
        url: req.originalUrl
      });

      return res.status(403).json({
        success: false,
        code: 4003,
        message: '权限不足',
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * 角色检查中间件工厂
 * @param {string|Array} roles - 允许的角色
 * @returns {Function} 中间件函数
 */
const requireRole = (roles) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        code: 4001,
        message: '用户未认证',
        timestamp: new Date().toISOString()
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      logger.security('Role access denied', {
        userId: req.user.id,
        studentId: req.user.studentId,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        ip: req.ip,
        url: req.originalUrl
      });

      return res.status(403).json({
        success: false,
        code: 4003,
        message: '角色权限不足',
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * 资源所有权检查中间件
 * 检查用户是否有权访问特定资源
 */
const checkResourceOwnership = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    if (!id) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '缺少资源ID',
        timestamp: new Date().toISOString()
      });
    }

    // 管理员可以访问所有资源
    if (req.user.role === 'admin') {
      return next();
    }

    // 查询资源是否属于当前用户
    const [rows] = await dbManager.todoSystemDB.execute(
      'SELECT user_id FROM todo_items WHERE id = ? LIMIT 1',
      [id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        code: 4004,
        message: '资源不存在',
        timestamp: new Date().toISOString()
      });
    }

    if (rows[0].user_id !== userId) {
      logger.security('Resource access denied', {
        userId,
        studentId: req.user.studentId,
        resourceId: id,
        resourceOwnerId: rows[0].user_id,
        ip: req.ip,
        url: req.originalUrl
      });

      return res.status(403).json({
        success: false,
        code: 4003,
        message: '无权访问此资源',
        timestamp: new Date().toISOString()
      });
    }

    // 将资源信息添加到请求对象
    req.resource = {
      id: parseInt(id),
      userId: rows[0].user_id
    };

    next();
  } catch (error) {
    logger.error('Resource ownership check failed', {
      error: error.message,
      userId: req.user?.id,
      resourceId: req.params.id
    });

    res.status(500).json({
      success: false,
      code: 5001,
      message: '服务器内部错误',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 可选认证中间件
 * 如果提供了token则验证，否则继续执行
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next();
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;

    if (!token) {
      return next();
    }

    try {
      const decoded = jwtUtils.verifyAccessToken(token);
      req.user = {
        id: decoded.userId,
        studentId: decoded.studentId,
        role: decoded.role,
        language: decoded.language,
        permissions: decoded.permissions || []
      };
    } catch (error) {
      // 可选认证失败时不返回错误，继续执行
      logger.warn('Optional authentication failed', {
        error: error.message,
        ip: req.ip
      });
    }

    next();
  } catch (error) {
    logger.error('Optional auth middleware error', {
      error: error.message,
      ip: req.ip
    });
    next();
  }
};

/**
 * 刷新令牌中间件
 * 检查访问令牌是否即将过期，如果是则自动刷新
 */
const autoRefreshToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next();
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;

    if (!token) {
      return next();
    }

    // 检查token是否即将过期 (5分钟内)
    if (jwtUtils.isTokenExpiringSoon(token, 5)) {
      // 设置响应头提示前端刷新token
      res.set('X-Token-Refresh-Required', 'true');
      
      logger.info('Token refresh required', {
        userId: req.user?.id,
        studentId: req.user?.studentId
      });
    }

    next();
  } catch (error) {
    logger.error('Auto refresh token middleware error', {
      error: error.message,
      userId: req.user?.id
    });
    next();
  }
};

module.exports = {
  authenticateToken,
  requirePermission,
  requireRole,
  checkResourceOwnership,
  optionalAuth,
  autoRefreshToken
};
