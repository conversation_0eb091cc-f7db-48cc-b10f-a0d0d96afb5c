#!/bin/bash

# 入学待办事项系统部署脚本
# 作者: Todo System Team
# 创建时间: 2024-12-05

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令不存在，请先安装"
        exit 1
    fi
}

# 检查Node.js版本
check_node_version() {
    local required_version="18"
    local current_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    
    if [ "$current_version" -lt "$required_version" ]; then
        log_error "Node.js版本过低，需要 >= $required_version，当前版本: $(node -v)"
        exit 1
    fi
    
    log_success "Node.js版本检查通过: $(node -v)"
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查必需的命令
    check_command "node"
    check_command "npm"
    check_command "git"
    check_command "mysql"
    
    # 检查Node.js版本
    check_node_version
    
    # 检查系统资源
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 512 ]; then
        log_warning "可用内存不足512MB，可能影响性能"
    fi
    
    local available_disk=$(df -h . | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "${available_disk%.*}" -lt 5 ]; then
        log_warning "可用磁盘空间不足5GB，可能影响运行"
    fi
    
    log_success "系统环境检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    mkdir -p monitoring_data
    mkdir -p scripts/python/analysis_output
    
    # 设置目录权限
    chmod 755 logs uploads backups monitoring_data
    chmod 644 logs/* 2>/dev/null || true
    
    log_success "目录创建完成"
}

# 安装后端依赖
install_backend_dependencies() {
    log_info "安装后端依赖..."
    
    cd backend
    
    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_error "backend/package.json 不存在"
        exit 1
    fi
    
    # 安装依赖
    npm ci --production
    
    cd ..
    log_success "后端依赖安装完成"
}

# 安装前端依赖
install_frontend_dependencies() {
    log_info "安装前端依赖..."
    
    # 安装H5移动端依赖
    if [ -d "mobile-h5" ]; then
        log_info "安装H5移动端依赖..."
        cd mobile-h5
        npm ci
        cd ..
    fi
    
    # 安装管理后台依赖
    if [ -d "admin-web" ]; then
        log_info "安装管理后台依赖..."
        cd admin-web
        npm ci
        cd ..
    fi
    
    log_success "前端依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 复制环境变量模板
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            log_warning "已创建 backend/.env 文件，请手动配置数据库连接等信息"
        else
            log_error "backend/.env.example 文件不存在"
            exit 1
        fi
    else
        log_info "backend/.env 文件已存在"
    fi
    
    log_success "环境变量配置完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 检查数据库连接配置
    if [ ! -f "backend/.env" ]; then
        log_error "请先配置 backend/.env 文件"
        exit 1
    fi
    
    # 读取数据库配置
    source backend/.env
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_USER" ] || [ -z "$DB_PASSWORD" ] || [ -z "$DB_NAME" ]; then
        log_error "数据库配置不完整，请检查 backend/.env 文件"
        exit 1
    fi
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &>/dev/null
    if [ $? -ne 0 ]; then
        log_error "数据库连接失败，请检查配置"
        exit 1
    fi
    
    # 执行数据库初始化脚本
    if [ -f "scripts/init-database.sql" ]; then
        log_info "执行数据库初始化脚本..."
        mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" < scripts/init-database.sql
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本不存在，跳过"
    fi
}

# 构建前端项目
build_frontend() {
    log_info "构建前端项目..."
    
    # 构建H5移动端
    if [ -d "mobile-h5" ]; then
        log_info "构建H5移动端..."
        cd mobile-h5
        npm run build
        cd ..
        log_success "H5移动端构建完成"
    fi
    
    # 构建管理后台
    if [ -d "admin-web" ]; then
        log_info "构建管理后台..."
        cd admin-web
        npm run build
        cd ..
        log_success "管理后台构建完成"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 检查PM2是否安装
    if ! command -v pm2 &> /dev/null; then
        log_info "安装PM2..."
        npm install -g pm2
    fi
    
    # 停止现有服务
    pm2 stop todo-system 2>/dev/null || true
    pm2 delete todo-system 2>/dev/null || true
    
    # 启动后端服务
    cd backend
    pm2 start server.js --name "todo-system" --env production
    cd ..
    
    # 保存PM2配置
    pm2 save
    pm2 startup
    
    log_success "服务启动完成"
}

# 配置Nginx (可选)
setup_nginx() {
    if command -v nginx &> /dev/null; then
        log_info "配置Nginx..."
        
        # 创建Nginx配置
        cat > /tmp/todo-system.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    
    # H5移动端
    location / {
        root /path/to/mobile-h5/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # 管理后台
    location /admin {
        alias /path/to/admin-web/dist;
        try_files $uri $uri/ /admin/index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件
    location /uploads {
        alias /path/to/uploads;
        expires 30d;
    }
}
EOF
        
        log_warning "Nginx配置文件已生成到 /tmp/todo-system.conf"
        log_warning "请手动复制到Nginx配置目录并修改路径"
    fi
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 等待服务启动
    sleep 5
    
    # 检查后端服务
    local backend_url="http://localhost:3001/health"
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$backend_url" || echo "000")
    
    if [ "$response" = "200" ]; then
        log_success "后端服务健康检查通过"
    else
        log_error "后端服务健康检查失败 (HTTP $response)"
        exit 1
    fi
    
    # 检查数据库连接
    source backend/.env
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &>/dev/null
    if [ $? -eq 0 ]; then
        log_success "数据库连接检查通过"
    else
        log_error "数据库连接检查失败"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "后端服务: http://localhost:3001"
    echo "健康检查: http://localhost:3001/health"
    echo "API文档: http://localhost:3001/api/v1"
    echo
    echo "=== 管理命令 ==="
    echo "查看服务状态: pm2 status"
    echo "查看日志: pm2 logs todo-system"
    echo "重启服务: pm2 restart todo-system"
    echo "停止服务: pm2 stop todo-system"
    echo
    echo "=== 配置文件 ==="
    echo "后端配置: backend/.env"
    echo "数据库脚本: scripts/init-database.sql"
    echo
    echo "=== 日志文件 ==="
    echo "应用日志: logs/"
    echo "PM2日志: ~/.pm2/logs/"
    echo
    log_warning "请确保已正确配置 backend/.env 文件中的数据库连接信息"
}

# 主函数
main() {
    log_info "开始部署入学待办事项系统..."
    
    # 解析命令行参数
    SKIP_DEPS=false
    SKIP_BUILD=false
    SKIP_DB=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-db)
                SKIP_DB=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-deps   跳过依赖安装"
                echo "  --skip-build  跳过前端构建"
                echo "  --skip-db     跳过数据库初始化"
                echo "  --help        显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_environment
    create_directories
    setup_environment
    
    if [ "$SKIP_DEPS" = false ]; then
        install_backend_dependencies
        install_frontend_dependencies
    fi
    
    if [ "$SKIP_DB" = false ]; then
        init_database
    fi
    
    if [ "$SKIP_BUILD" = false ]; then
        build_frontend
    fi
    
    start_services
    setup_nginx
    health_check
    show_deployment_info
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
