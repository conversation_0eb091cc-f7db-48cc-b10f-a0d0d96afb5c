const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const UserService = require('../services/UserService');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 所有用户路由都需要认证
router.use(authenticateToken);

/**
 * 获取当前用户信息
 * GET /api/v1/user/profile
 */
router.get('/profile',
  requirePermission('profile:read'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    try {
      const userService = new UserService(dbManager);
      const userProfile = await userService.getUserProfile(userId);

      res.json({
        success: true,
        code: 1000,
        message: '获取用户信息成功',
        data: userProfile,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get user profile', {
        userId,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 更新用户偏好设置
 * PUT /api/v1/user/preferences
 */
router.put('/preferences',
  requirePermission('profile:update'),
  [
    body('language')
      .optional()
      .isIn(['zh-cn', 'en-us'])
      .withMessage('语言设置无效'),
    body('theme')
      .optional()
      .isIn(['light', 'dark', 'auto'])
      .withMessage('主题设置无效'),
    body('notifications')
      .optional()
      .isBoolean()
      .withMessage('通知设置必须是布尔值'),
    body('timezone')
      .optional()
      .isString()
      .withMessage('时区设置无效')
  ],
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const userId = req.user.id;
    const preferences = req.body;
    const dbManager = req.app.locals.dbManager;

    try {
      const userService = new UserService(dbManager);
      const updatedPreferences = await userService.updateUserPreferences(userId, preferences);

      // 记录用户偏好更新日志
      logger.audit('User preferences updated', {
        userId,
        studentId: req.user.studentId,
        preferences,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '偏好设置更新成功',
        data: updatedPreferences,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to update user preferences', {
        userId,
        preferences,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 获取用户统计数据
 * GET /api/v1/user/stats
 */
router.get('/stats',
  requirePermission('todo:read'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    try {
      const userService = new UserService(dbManager);
      const stats = await userService.getUserStats(userId);

      res.json({
        success: true,
        code: 1000,
        message: '获取用户统计数据成功',
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get user stats', {
        userId,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 更新用户头像
 * PUT /api/v1/user/avatar
 */
router.put('/avatar',
  requirePermission('profile:update'),
  [
    body('avatarUrl')
      .isURL()
      .withMessage('头像URL格式无效')
      .isLength({ max: 500 })
      .withMessage('头像URL长度不能超过500字符')
  ],
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const userId = req.user.id;
    const { avatarUrl } = req.body;
    const dbManager = req.app.locals.dbManager;

    try {
      const userService = new UserService(dbManager);
      await userService.updateUserAvatar(userId, avatarUrl);

      // 记录头像更新日志
      logger.audit('User avatar updated', {
        userId,
        studentId: req.user.studentId,
        avatarUrl,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '头像更新成功',
        data: { avatarUrl },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to update user avatar', {
        userId,
        avatarUrl,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 获取用户通知列表
 * GET /api/v1/user/notifications
 */
router.get('/notifications',
  requirePermission('profile:read'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, limit = 20, unreadOnly = false } = req.query;
    const dbManager = req.app.locals.dbManager;

    try {
      const userService = new UserService(dbManager);
      const notifications = await userService.getUserNotifications(userId, {
        page: parseInt(page),
        limit: parseInt(limit),
        unreadOnly: unreadOnly === 'true'
      });

      res.json({
        success: true,
        code: 1000,
        message: '获取通知列表成功',
        data: notifications,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get user notifications', {
        userId,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 标记通知为已读
 * PUT /api/v1/user/notifications/:id/read
 */
router.put('/notifications/:id/read',
  requirePermission('profile:update'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const notificationId = parseInt(req.params.id);
    const dbManager = req.app.locals.dbManager;

    if (!notificationId || isNaN(notificationId)) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '通知ID无效',
        timestamp: new Date().toISOString()
      });
    }

    try {
      const userService = new UserService(dbManager);
      await userService.markNotificationAsRead(userId, notificationId);

      res.json({
        success: true,
        code: 1000,
        message: '通知已标记为已读',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to mark notification as read', {
        userId,
        notificationId,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 标记所有通知为已读
 * PUT /api/v1/user/notifications/read-all
 */
router.put('/notifications/read-all',
  requirePermission('profile:update'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    try {
      const userService = new UserService(dbManager);
      const updatedCount = await userService.markAllNotificationsAsRead(userId);

      // 记录批量标记已读日志
      logger.audit('All notifications marked as read', {
        userId,
        studentId: req.user.studentId,
        updatedCount,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '所有通知已标记为已读',
        data: { updatedCount },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to mark all notifications as read', {
        userId,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 删除通知
 * DELETE /api/v1/user/notifications/:id
 */
router.delete('/notifications/:id',
  requirePermission('profile:update'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const notificationId = parseInt(req.params.id);
    const dbManager = req.app.locals.dbManager;

    if (!notificationId || isNaN(notificationId)) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '通知ID无效',
        timestamp: new Date().toISOString()
      });
    }

    try {
      const userService = new UserService(dbManager);
      await userService.deleteNotification(userId, notificationId);

      // 记录通知删除日志
      logger.audit('Notification deleted', {
        userId,
        studentId: req.user.studentId,
        notificationId,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '通知删除成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to delete notification', {
        userId,
        notificationId,
        error: error.message
      });
      throw error;
    }
  })
);

module.exports = router;
