import { api } from './index'

export const authAPI = {
  // 用户登录（支持学号登录和管理员登录）
  login(credentials) {
    if (credentials.loginType === 'student') {
      // 学号登录
      return api.post('/auth/student-login', {
        studentId: credentials.studentId
      })
    } else {
      // 管理员登录
      return api.post('/auth/login', credentials)
    }
  },

  // 学号验证（检查学号是否存在于数据库）
  validateStudentId(studentId) {
    return api.post('/auth/validate-student-id', { studentId })
  },
  
  // 用户注册
  register(userData) {
    return api.post('/auth/register', userData)
  },
  
  // 用户登出
  logout() {
    return api.post('/auth/logout')
  },
  
  // 获取用户信息
  getUserInfo() {
    return api.get('/auth/me')
  },
  
  // 更新用户信息
  updateUserInfo(data) {
    return api.put('/auth/me', data)
  },
  
  // 修改密码
  changePassword(passwordData) {
    return api.post('/auth/change-password', passwordData)
  },
  
  // 忘记密码 - 发送验证码
  sendResetCode(data) {
    return api.post('/auth/forgot-password', data)
  },
  
  // 重置密码
  resetPassword(data) {
    return api.post('/auth/reset-password', data)
  },
  
  // 刷新Token
  refreshToken() {
    return api.post('/auth/refresh-token')
  },
  
  // 验证Token
  verifyToken() {
    return api.get('/auth/verify-token')
  },
  
  // 发送短信验证码
  sendSmsCode(data) {
    return api.post('/auth/send-sms-code', data)
  },
  
  // 验证短信验证码
  verifySmsCode(data) {
    return api.post('/auth/verify-sms-code', data)
  },
  
  // 发送邮箱验证码
  sendEmailCode(data) {
    return api.post('/auth/send-email-code', data)
  },
  
  // 验证邮箱验证码
  verifyEmailCode(data) {
    return api.post('/auth/verify-email-code', data)
  },
  
  // 绑定手机号
  bindPhone(data) {
    return api.post('/auth/bind-phone', data)
  },
  
  // 绑定邮箱
  bindEmail(data) {
    return api.post('/auth/bind-email', data)
  },
  
  // 解绑手机号
  unbindPhone(data) {
    return api.post('/auth/unbind-phone', data)
  },
  
  // 解绑邮箱
  unbindEmail(data) {
    return api.post('/auth/unbind-email', data)
  },
  
  // 上传头像
  uploadAvatar(formData) {
    return api.upload('/auth/upload-avatar', formData)
  },
  
  // 获取用户设置
  getUserSettings() {
    return api.get('/auth/settings')
  },
  
  // 更新用户设置
  updateUserSettings(data) {
    return api.put('/auth/settings', data)
  },
  
  // 注销账户
  deleteAccount(data) {
    return api.post('/auth/delete-account', data)
  }
}
