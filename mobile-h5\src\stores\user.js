import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '../api/auth'
import { removeToken, setToken } from '../utils/storage'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const permissions = ref([])
  const loginTime = ref(null)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const userName = computed(() => userInfo.value?.name || '')
  const userAvatar = computed(() => userInfo.value?.avatar || '')
  const userRole = computed(() => userInfo.value?.role || 'student')
  const studentId = computed(() => userInfo.value?.studentId || '')
  
  // 方法
  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials)
      const { token: newToken, user } = response.data
      
      // 保存token和用户信息
      token.value = newToken
      userInfo.value = user
      loginTime.value = new Date().toISOString()
      
      // 持久化存储
      setToken(newToken)
      
      console.log('登录成功:', user.name)
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }
  
  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData)
      console.log('注册成功')
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }
  
  const logout = async () => {
    try {
      // 调用登出接口
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      permissions.value = []
      loginTime.value = null
      
      // 清除持久化存储
      removeToken()
      
      console.log('已登出')
    }
  }
  
  const getUserInfo = async () => {
    try {
      if (!token.value) {
        throw new Error('未登录')
      }
      
      const response = await authAPI.getUserInfo()
      userInfo.value = response.data
      
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期
      if (error.response?.status === 401) {
        await logout()
      }
      throw error
    }
  }
  
  const updateUserInfo = async (data) => {
    try {
      const response = await authAPI.updateUserInfo(data)
      userInfo.value = { ...userInfo.value, ...response.data }
      
      console.log('用户信息更新成功')
      return response
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }
  
  const changePassword = async (passwordData) => {
    try {
      const response = await authAPI.changePassword(passwordData)
      console.log('密码修改成功')
      return response
    } catch (error) {
      console.error('密码修改失败:', error)
      throw error
    }
  }
  
  const resetPassword = async (data) => {
    try {
      const response = await authAPI.resetPassword(data)
      console.log('密码重置成功')
      return response
    } catch (error) {
      console.error('密码重置失败:', error)
      throw error
    }
  }
  
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken()
      const { token: newToken } = response.data
      
      token.value = newToken
      setToken(newToken)
      
      console.log('Token刷新成功')
      return response
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout()
      throw error
    }
  }
  
  const checkLoginStatus = async () => {
    try {
      if (!token.value) {
        return false
      }
      
      // 检查token是否有效
      await getUserInfo()
      return true
    } catch (error) {
      console.error('登录状态检查失败:', error)
      await logout()
      return false
    }
  }
  
  const refreshUserInfo = async () => {
    if (isLoggedIn.value) {
      try {
        await getUserInfo()
      } catch (error) {
        console.error('刷新用户信息失败:', error)
      }
    }
  }
  
  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role) => {
    return userRole.value === role
  }
  
  return {
    // 状态
    token,
    userInfo,
    permissions,
    loginTime,
    
    // 计算属性
    isLoggedIn,
    userName,
    userAvatar,
    userRole,
    studentId,
    
    // 方法
    login,
    register,
    logout,
    getUserInfo,
    updateUserInfo,
    changePassword,
    resetPassword,
    refreshToken,
    checkLoginStatus,
    refreshUserInfo,
    hasPermission,
    hasRole
  }
})
