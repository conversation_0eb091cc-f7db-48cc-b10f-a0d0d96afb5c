{"common": {"confirm": "Confirm", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "loading": "Loading...", "noData": "No Data", "retry": "Retry", "back": "Back", "next": "Next", "prev": "Previous", "submit": "Submit", "reset": "Reset", "close": "Close", "open": "Open", "view": "View", "more": "More", "all": "All", "yes": "Yes", "no": "No", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "phone": "Phone", "verificationCode": "Verification Code", "sendCode": "Send Code", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "oldPassword": "Old Password", "newPassword": "New Password", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful", "registerSuccess": "Registration successful", "passwordChanged": "Password changed successfully", "codeSent": "Verification code sent", "invalidCredentials": "Invalid username or password", "userExists": "User already exists", "userNotFound": "User not found", "invalidCode": "Invalid verification code", "codeExpired": "Verification code expired", "passwordMismatch": "Passwords do not match", "rememberMe": "Remember me", "autoLogin": "Auto login"}, "todo": {"title": "Todo", "todoList": "Todo List", "todoDetail": "<PERSON><PERSON>", "addTodo": "Add <PERSON>", "editTodo": "<PERSON>", "deleteTodo": "Delete Todo", "completeTodo": "Complete Todo", "todoTitle": "Title", "todoDescription": "Description", "todoCategory": "Category", "todoPriority": "Priority", "todoDueDate": "Due Date", "todoStatus": "Status", "todoProgress": "Progress", "todoAttachments": "Attachments", "todoComments": "Comments", "todoReminder": "Reminder", "todoCreatedAt": "Created At", "todoUpdatedAt": "Updated At", "todoCompletedAt": "Completed At", "status": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "category": {"academic": "Academic", "administrative": "Administrative", "accommodation": "Accommodation", "financial": "Financial", "health": "Health", "social": "Social", "other": "Other"}, "filter": {"all": "All", "today": "Today", "week": "This Week", "overdue": "Overdue", "completed": "Completed", "important": "Important"}, "stats": {"total": "Total", "completed": "Completed", "pending": "Pending", "overdue": "Overdue", "completionRate": "Completion Rate"}, "messages": {"todoCreated": "<PERSON><PERSON> created successfully", "todoUpdated": "Todo updated successfully", "todoDeleted": "<PERSON><PERSON> deleted successfully", "todoCompleted": "Todo completed", "todoRestored": "Todo restored", "confirmDelete": "Are you sure you want to delete this todo?", "confirmComplete": "Are you sure you want to complete this todo?", "noTodos": "No todos", "loadMore": "Load More", "noMore": "No more items"}}, "profile": {"profile": "Profile", "userInfo": "User Info", "settings": "Settings", "about": "About", "name": "Name", "studentId": "Student ID", "major": "Major", "grade": "Grade", "class": "Class", "gender": "Gender", "birthday": "Birthday", "avatar": "Avatar", "bio": "Bio", "contact": "Contact", "address": "Address", "emergencyContact": "Emergency Contact", "editProfile": "Edit Profile", "changeAvatar": "Change Avatar", "profileUpdated": "Profile updated successfully", "avatarUpdated": "Avatar updated successfully"}, "settings": {"settings": "Settings", "general": "General", "notification": "Notification", "privacy": "Privacy Policy", "security": "Security", "about": "About", "language": "Language", "theme": "Theme", "fontSize": "Font Size", "autoSync": "Auto Sync", "pushNotification": "Push Notification", "emailNotification": "Email Notification", "smsNotification": "SMS Notification", "soundEnabled": "Sound", "vibrationEnabled": "Vibration", "dataUsage": "Data Usage", "cacheSize": "<PERSON><PERSON>", "clearCache": "<PERSON>ache", "version": "Version", "checkUpdate": "Check Update", "feedback": "<PERSON><PERSON><PERSON>", "help": "Help", "terms": "Terms of Service", "logout": "Logout", "deleteAccount": "Delete Account", "themes": {"light": "Light", "dark": "Dark", "auto": "Auto"}, "languages": {"zh-CN": "简体中文", "en-US": "English"}, "messages": {"settingsSaved": "Setting<PERSON> saved", "cacheCleared": "<PERSON><PERSON> cleared", "confirmLogout": "Are you sure you want to logout?", "confirmDeleteAccount": "Are you sure you want to delete your account? This action cannot be undone!"}}, "home": {"welcome": "Welcome", "dashboard": "Dashboard", "quickActions": "Quick Actions", "recentTodos": "Recent Todos", "upcomingTodos": "Upcoming Todos", "statistics": "Statistics", "progress": "Progress", "notifications": "Notifications", "announcements": "Announcements", "weather": "Weather", "calendar": "Calendar", "schedule": "Schedule", "goodMorning": "Good Morning", "goodAfternoon": "Good Afternoon", "goodEvening": "Good Evening", "todayTasks": "Today's Tasks", "completedToday": "Completed Today", "overdueItems": "Overdue Items"}, "navigation": {"home": "Home", "todos": "Todos", "profile": "Profile", "settings": "Settings", "back": "Back", "menu": "<PERSON><PERSON>"}, "errors": {"networkError": "Network connection failed", "serverError": "Server error", "notFound": "Page not found", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "validationError": "Validation failed", "unknownError": "Unknown error", "retry": "Retry", "goHome": "Go Home"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 8 characters", "confirmPassword": "Passwords do not match", "minLength": "Minimum {min} characters required", "maxLength": "Maximum {max} characters allowed", "min": "Minimum value is {min}", "max": "Maximum value is {max}", "pattern": "Invalid format", "url": "Please enter a valid URL", "number": "Please enter a valid number", "integer": "Please enter an integer", "date": "Please select a date", "time": "Please select a time"}}