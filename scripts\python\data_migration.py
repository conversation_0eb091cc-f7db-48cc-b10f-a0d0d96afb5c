#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入学待办事项系统 - 数据迁移工具

这个脚本用于从旧系统迁移数据到新系统，
包括用户数据、待办事项数据等的迁移和转换。

作者: Todo System Team
创建时间: 2024-12-05
"""

import os
import sys
import json
import pandas as pd
import mysql.connector
from mysql.connector import Error
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import hashlib
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_migration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class DataMigrator:
    """数据迁移器"""
    
    def __init__(self, source_config: Dict, target_config: Dict):
        """
        初始化数据迁移器
        
        Args:
            source_config: 源数据库配置
            target_config: 目标数据库配置
        """
        self.source_config = source_config
        self.target_config = target_config
        self.source_conn = None
        self.target_conn = None
        self.migration_log = []
        
    def connect_databases(self) -> bool:
        """
        连接源数据库和目标数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 连接源数据库
            self.source_conn = mysql.connector.connect(**self.source_config)
            logger.info("源数据库连接成功")
            
            # 连接目标数据库
            self.target_conn = mysql.connector.connect(**self.target_config)
            logger.info("目标数据库连接成功")
            
            return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.source_conn and self.source_conn.is_connected():
            self.source_conn.close()
            logger.info("源数据库连接已关闭")
            
        if self.target_conn and self.target_conn.is_connected():
            self.target_conn.close()
            logger.info("目标数据库连接已关闭")
    
    def execute_query(self, connection, query: str, params: Tuple = None) -> List:
        """
        执行SQL查询
        
        Args:
            connection: 数据库连接
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List: 查询结果
        """
        try:
            cursor = connection.cursor(dictionary=True)
            cursor.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
            else:
                connection.commit()
                result = cursor.rowcount
                
            cursor.close()
            return result
        except Error as e:
            logger.error(f"查询执行失败: {e}")
            logger.error(f"查询语句: {query}")
            return []
    
    def migrate_users(self) -> bool:
        """
        迁移用户数据
        
        Returns:
            bool: 迁移是否成功
        """
        logger.info("开始迁移用户数据...")
        
        try:
            # 从源数据库查询用户数据
            source_query = """
            SELECT 
                student_id,
                student_name,
                email,
                phone,
                created_time,
                last_login_time,
                status
            FROM old_users
            WHERE status = 'active'
            """
            
            source_users = self.execute_query(self.source_conn, source_query)
            logger.info(f"从源数据库获取到 {len(source_users)} 个用户")
            
            migrated_count = 0
            failed_count = 0
            
            for user in source_users:
                try:
                    # 检查用户是否已存在
                    check_query = """
                    SELECT id FROM users WHERE student_id = %s
                    """
                    existing = self.execute_query(
                        self.target_conn, 
                        check_query, 
                        (user['student_id'],)
                    )
                    
                    if existing:
                        logger.warning(f"用户 {user['student_id']} 已存在，跳过")
                        continue
                    
                    # 插入新用户
                    insert_query = """
                    INSERT INTO users (
                        student_id, real_name, nickname, role, 
                        is_active, created_at, last_login_time
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    self.execute_query(
                        self.target_conn,
                        insert_query,
                        (
                            user['student_id'],
                            user['student_name'],
                            user['student_name'],
                            'student',
                            1 if user['status'] == 'active' else 0,
                            user['created_time'],
                            user['last_login_time']
                        )
                    )
                    
                    migrated_count += 1
                    
                    if migrated_count % 100 == 0:
                        logger.info(f"已迁移 {migrated_count} 个用户")
                        
                except Exception as e:
                    logger.error(f"迁移用户 {user['student_id']} 失败: {e}")
                    failed_count += 1
            
            logger.info(f"用户迁移完成: 成功 {migrated_count}, 失败 {failed_count}")
            
            self.migration_log.append({
                'table': 'users',
                'migrated': migrated_count,
                'failed': failed_count,
                'timestamp': datetime.now().isoformat()
            })
            
            return failed_count == 0
            
        except Exception as e:
            logger.error(f"用户迁移过程中发生错误: {e}")
            return False
    
    def migrate_todo_templates(self) -> bool:
        """
        迁移待办模板数据
        
        Returns:
            bool: 迁移是否成功
        """
        logger.info("开始迁移待办模板数据...")
        
        try:
            # 预定义的待办模板
            templates = [
                {
                    'title_zh': '学费缴纳',
                    'title_en': 'Tuition Payment',
                    'desc_zh': '请在规定时间内完成学费缴纳，逾期将影响正常入学。',
                    'desc_en': 'Please complete tuition payment within the specified time.',
                    'category': 'financial',
                    'priority': 'high',
                    'duration': 30
                },
                {
                    'title_zh': '宿舍分配',
                    'title_en': 'Dormitory Assignment',
                    'desc_zh': '选择宿舍并完成入住手续。',
                    'desc_en': 'Select dormitory and complete check-in procedures.',
                    'category': 'dormitory',
                    'priority': 'medium',
                    'duration': 45
                },
                {
                    'title_zh': '体检报告',
                    'title_en': 'Medical Examination',
                    'desc_zh': '提交入学体检报告。',
                    'desc_en': 'Submit medical examination report.',
                    'category': 'health',
                    'priority': 'high',
                    'duration': 60
                },
                {
                    'title_zh': '课程选择',
                    'title_en': 'Course Selection',
                    'desc_zh': '选择本学期的课程。',
                    'desc_en': 'Select courses for this semester.',
                    'category': 'academic',
                    'priority': 'medium',
                    'duration': 90
                },
                {
                    'title_zh': '学生证办理',
                    'title_en': 'Student ID Card',
                    'desc_zh': '办理学生证件。',
                    'desc_en': 'Apply for student ID card.',
                    'category': 'other',
                    'priority': 'low',
                    'duration': 20
                }
            ]
            
            migrated_count = 0
            
            for template in templates:
                try:
                    # 检查模板是否已存在
                    check_query = """
                    SELECT id FROM todo_templates 
                    WHERE JSON_EXTRACT(title, '$.zh-cn') = %s
                    """
                    existing = self.execute_query(
                        self.target_conn,
                        check_query,
                        (template['title_zh'],)
                    )
                    
                    if existing:
                        logger.warning(f"模板 {template['title_zh']} 已存在，跳过")
                        continue
                    
                    # 插入新模板
                    insert_query = """
                    INSERT INTO todo_templates (
                        title, description, category, priority, 
                        estimated_duration, is_active, created_by, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                    """
                    
                    title_json = json.dumps({
                        'zh-cn': template['title_zh'],
                        'en-us': template['title_en']
                    })
                    
                    desc_json = json.dumps({
                        'zh-cn': template['desc_zh'],
                        'en-us': template['desc_en']
                    })
                    
                    self.execute_query(
                        self.target_conn,
                        insert_query,
                        (
                            title_json,
                            desc_json,
                            template['category'],
                            template['priority'],
                            template['duration'],
                            1,
                            1  # 假设管理员ID为1
                        )
                    )
                    
                    migrated_count += 1
                    logger.info(f"已迁移模板: {template['title_zh']}")
                    
                except Exception as e:
                    logger.error(f"迁移模板 {template['title_zh']} 失败: {e}")
            
            logger.info(f"模板迁移完成: 成功 {migrated_count}")
            
            self.migration_log.append({
                'table': 'todo_templates',
                'migrated': migrated_count,
                'failed': 0,
                'timestamp': datetime.now().isoformat()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"模板迁移过程中发生错误: {e}")
            return False
    
    def migrate_todo_items(self) -> bool:
        """
        迁移待办事项数据
        
        Returns:
            bool: 迁移是否成功
        """
        logger.info("开始迁移待办事项数据...")
        
        try:
            # 从源数据库查询待办事项
            source_query = """
            SELECT 
                student_id,
                task_title,
                task_description,
                task_category,
                task_status,
                task_priority,
                due_date,
                completed_date,
                created_time
            FROM old_tasks
            WHERE created_time >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            """
            
            source_todos = self.execute_query(self.source_conn, source_query)
            logger.info(f"从源数据库获取到 {len(source_todos)} 个待办事项")
            
            migrated_count = 0
            failed_count = 0
            
            for todo in source_todos:
                try:
                    # 获取用户ID
                    user_query = """
                    SELECT id FROM users WHERE student_id = %s
                    """
                    user_result = self.execute_query(
                        self.target_conn,
                        user_query,
                        (todo['student_id'],)
                    )
                    
                    if not user_result:
                        logger.warning(f"找不到学号为 {todo['student_id']} 的用户，跳过")
                        continue
                    
                    user_id = user_result[0]['id']
                    
                    # 状态映射
                    status_mapping = {
                        'pending': 'pending',
                        'in_progress': 'processing',
                        'completed': 'completed',
                        'cancelled': 'cancelled'
                    }
                    
                    # 分类映射
                    category_mapping = {
                        'study': 'academic',
                        'living': 'dormitory',
                        'finance': 'financial',
                        'health': 'health',
                        'other': 'other'
                    }
                    
                    # 优先级映射
                    priority_mapping = {
                        'low': 'low',
                        'normal': 'medium',
                        'high': 'high',
                        'urgent': 'urgent'
                    }
                    
                    # 插入待办事项
                    insert_query = """
                    INSERT INTO todo_items (
                        user_id, title, description, category, status, 
                        priority, due_date, completed_at, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    self.execute_query(
                        self.target_conn,
                        insert_query,
                        (
                            user_id,
                            todo['task_title'],
                            todo['task_description'],
                            category_mapping.get(todo['task_category'], 'other'),
                            status_mapping.get(todo['task_status'], 'pending'),
                            priority_mapping.get(todo['task_priority'], 'medium'),
                            todo['due_date'],
                            todo['completed_date'],
                            todo['created_time']
                        )
                    )
                    
                    migrated_count += 1
                    
                    if migrated_count % 100 == 0:
                        logger.info(f"已迁移 {migrated_count} 个待办事项")
                        
                except Exception as e:
                    logger.error(f"迁移待办事项失败: {e}")
                    failed_count += 1
            
            logger.info(f"待办事项迁移完成: 成功 {migrated_count}, 失败 {failed_count}")
            
            self.migration_log.append({
                'table': 'todo_items',
                'migrated': migrated_count,
                'failed': failed_count,
                'timestamp': datetime.now().isoformat()
            })
            
            return failed_count == 0
            
        except Exception as e:
            logger.error(f"待办事项迁移过程中发生错误: {e}")
            return False
    
    def create_admin_user(self) -> bool:
        """
        创建默认管理员用户
        
        Returns:
            bool: 创建是否成功
        """
        logger.info("创建默认管理员用户...")
        
        try:
            # 检查管理员是否已存在
            check_query = """
            SELECT id FROM users WHERE username = 'admin'
            """
            existing = self.execute_query(self.target_conn, check_query)
            
            if existing:
                logger.info("管理员用户已存在")
                return True
            
            # 创建管理员用户
            import bcrypt
            password_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            insert_query = """
            INSERT INTO users (
                username, password_hash, real_name, nickname, 
                role, is_active, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, NOW())
            """
            
            self.execute_query(
                self.target_conn,
                insert_query,
                ('admin', password_hash, '系统管理员', '管理员', 'admin', 1)
            )
            
            logger.info("默认管理员用户创建成功 (用户名: admin, 密码: admin123)")
            return True
            
        except Exception as e:
            logger.error(f"创建管理员用户失败: {e}")
            return False
    
    def generate_migration_report(self) -> str:
        """
        生成迁移报告
        
        Returns:
            str: 报告文件路径
        """
        logger.info("生成迁移报告...")
        
        report = {
            'migration_date': datetime.now().isoformat(),
            'source_database': self.source_config['database'],
            'target_database': self.target_config['database'],
            'migration_log': self.migration_log,
            'summary': {
                'total_tables': len(self.migration_log),
                'total_migrated': sum(log['migrated'] for log in self.migration_log),
                'total_failed': sum(log['failed'] for log in self.migration_log)
            }
        }
        
        report_path = f'migration_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"迁移报告已保存到: {report_path}")
        return report_path
    
    def run_migration(self):
        """运行完整的数据迁移"""
        logger.info("开始数据迁移...")
        
        if not self.connect_databases():
            logger.error("无法连接数据库，迁移终止")
            return
        
        try:
            # 执行迁移步骤
            steps = [
                ('创建管理员用户', self.create_admin_user),
                ('迁移用户数据', self.migrate_users),
                ('迁移待办模板', self.migrate_todo_templates),
                ('迁移待办事项', self.migrate_todo_items)
            ]
            
            for step_name, step_func in steps:
                logger.info(f"执行步骤: {step_name}")
                success = step_func()
                if not success:
                    logger.error(f"步骤 {step_name} 失败")
                    break
                logger.info(f"步骤 {step_name} 完成")
            
            # 生成报告
            report_path = self.generate_migration_report()
            logger.info("数据迁移完成！")
            logger.info(f"迁移报告: {report_path}")
            
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {e}")
        finally:
            self.close_connections()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='入学待办事项系统数据迁移工具')
    
    # 源数据库配置
    parser.add_argument('--source-host', default='localhost', help='源数据库主机地址')
    parser.add_argument('--source-port', type=int, default=3306, help='源数据库端口')
    parser.add_argument('--source-user', required=True, help='源数据库用户名')
    parser.add_argument('--source-password', required=True, help='源数据库密码')
    parser.add_argument('--source-database', required=True, help='源数据库名称')
    
    # 目标数据库配置
    parser.add_argument('--target-host', default='localhost', help='目标数据库主机地址')
    parser.add_argument('--target-port', type=int, default=3306, help='目标数据库端口')
    parser.add_argument('--target-user', default='todo_user', help='目标数据库用户名')
    parser.add_argument('--target-password', required=True, help='目标数据库密码')
    parser.add_argument('--target-database', default='school_enrollment_todo', help='目标数据库名称')
    
    args = parser.parse_args()
    
    # 数据库配置
    source_config = {
        'host': args.source_host,
        'port': args.source_port,
        'user': args.source_user,
        'password': args.source_password,
        'database': args.source_database,
        'charset': 'utf8mb4'
    }
    
    target_config = {
        'host': args.target_host,
        'port': args.target_port,
        'user': args.target_user,
        'password': args.target_password,
        'database': args.target_database,
        'charset': 'utf8mb4'
    }
    
    # 创建迁移器并运行迁移
    migrator = DataMigrator(source_config, target_config)
    migrator.run_migration()


if __name__ == '__main__':
    main()
