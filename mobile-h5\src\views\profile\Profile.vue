<template>
  <div class="profile-page">
    <!-- 头部 -->
    <div class="profile-header">
      <div class="user-info">
        <div class="avatar-section">
          <van-image
            :src="userStore.userAvatar"
            round
            width="80"
            height="80"
            fit="cover"
            @click="changeAvatar"
          >
            <template #error>
              <van-icon name="user-o" size="40" />
            </template>
          </van-image>
          <div class="avatar-edit">
            <van-icon name="camera-o" />
          </div>
        </div>
        
        <div class="user-details">
          <h2 class="username">{{ userStore.userName || '未设置姓名' }}</h2>
          <p class="user-id">学号：{{ userStore.studentId || '未设置' }}</p>
          <p class="user-role">{{ getRoleText(userStore.userRole) }}</p>
        </div>
        
        <van-icon name="setting-o" class="settings-icon" @click="goToSettings" />
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-number">{{ todoStats.total }}</div>
          <div class="stat-label">总待办</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ todoStats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ todoStats.completionRate }}%</div>
          <div class="stat-label">完成率</div>
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <!-- 个人信息 -->
      <div class="menu-group">
        <h3 class="group-title">个人信息</h3>
        <van-cell-group>
          <van-cell
            title="编辑资料"
            icon="edit"
            is-link
            @click="editProfile"
          />
          <van-cell
            title="修改密码"
            icon="lock"
            is-link
            @click="changePassword"
          />
          <van-cell
            title="绑定手机"
            icon="phone-o"
            is-link
            :value="userStore.userInfo?.phone || '未绑定'"
            @click="bindPhone"
          />
          <van-cell
            title="绑定邮箱"
            icon="envelop-o"
            is-link
            :value="userStore.userInfo?.email || '未绑定'"
            @click="bindEmail"
          />
        </van-cell-group>
      </div>

      <!-- 应用功能 -->
      <div class="menu-group">
        <h3 class="group-title">应用功能</h3>
        <van-cell-group>
          <van-cell
            title="我的收藏"
            icon="star-o"
            is-link
            @click="showFavorites"
          />
          <van-cell
            title="历史记录"
            icon="clock-o"
            is-link
            @click="showHistory"
          />
          <van-cell
            title="数据统计"
            icon="chart-trending-o"
            is-link
            @click="showStatistics"
          />
          <van-cell
            title="导入导出"
            icon="exchange"
            is-link
            @click="showImportExport"
          />
        </van-cell-group>
      </div>

      <!-- 系统设置 -->
      <div class="menu-group">
        <h3 class="group-title">系统设置</h3>
        <van-cell-group>
          <van-cell
            title="通知设置"
            icon="bell-o"
            is-link
            @click="notificationSettings"
          />
          <van-cell
            title="隐私设置"
            icon="shield-o"
            is-link
            @click="privacySettings"
          />
          <van-cell
            title="语言设置"
            icon="globe-o"
            is-link
            :value="getLanguageText(appStore.locale)"
            @click="languageSettings"
          />
          <van-cell
            title="主题设置"
            icon="palette-o"
            is-link
            :value="getThemeText(appStore.theme)"
            @click="themeSettings"
          />
        </van-cell-group>
      </div>

      <!-- 帮助支持 -->
      <div class="menu-group">
        <h3 class="group-title">帮助支持</h3>
        <van-cell-group>
          <van-cell
            title="帮助中心"
            icon="question-o"
            is-link
            @click="showHelp"
          />
          <van-cell
            title="意见反馈"
            icon="comment-o"
            is-link
            @click="showFeedback"
          />
          <van-cell
            title="关于我们"
            icon="info-o"
            is-link
            @click="showAbout"
          />
          <van-cell
            title="检查更新"
            icon="upgrade"
            is-link
            :value="appStore.appVersion"
            @click="checkUpdate"
          />
        </van-cell-group>
      </div>

      <!-- 退出登录 -->
      <div class="logout-section">
        <van-button
          type="danger"
          size="large"
          block
          @click="logout"
        >
          退出登录
        </van-button>
      </div>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="todo-list-o" to="/todo">待办</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 头像上传 -->
    <van-uploader
      ref="uploaderRef"
      v-model="fileList"
      :after-read="afterRead"
      :max-count="1"
      accept="image/*"
      style="display: none"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useUserStore } from '../../stores/user'
import { useAppStore } from '../../stores/app'
import { useTodoStore } from '../../stores/todo'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()
const todoStore = useTodoStore()

const { todoStats } = storeToRefs(todoStore)

// 响应式数据
const activeTab = ref(2)
const uploaderRef = ref()
const fileList = ref([])

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 方法
const changeAvatar = () => {
  uploaderRef.value?.chooseFile()
}

const afterRead = async (file) => {
  try {
    appStore.setGlobalLoading(true)
    
    // 创建FormData
    const formData = new FormData()
    formData.append('avatar', file.file)
    
    // 上传头像
    // await authAPI.uploadAvatar(formData)
    
    // 模拟上传成功
    setTimeout(() => {
      appStore.showToast('头像更新成功', 'success')
      appStore.setGlobalLoading(false)
    }, 1000)
    
  } catch (error) {
    console.error('上传头像失败:', error)
    appStore.showToast('上传失败', 'error')
    appStore.setGlobalLoading(false)
  }
}

const editProfile = () => {
  router.push('/profile/edit')
}

const changePassword = () => {
  router.push('/profile/change-password')
}

const bindPhone = () => {
  router.push('/profile/bind-phone')
}

const bindEmail = () => {
  router.push('/profile/bind-email')
}

const goToSettings = () => {
  router.push('/settings')
}

const showFavorites = () => {
  appStore.showToast('收藏功能开发中', 'info')
}

const showHistory = () => {
  appStore.showToast('历史记录功能开发中', 'info')
}

const showStatistics = () => {
  appStore.showToast('数据统计功能开发中', 'info')
}

const showImportExport = () => {
  appStore.showToast('导入导出功能开发中', 'info')
}

const notificationSettings = () => {
  router.push('/settings/notification')
}

const privacySettings = () => {
  router.push('/settings/privacy')
}

const languageSettings = () => {
  router.push('/settings/language')
}

const themeSettings = () => {
  router.push('/settings/theme')
}

const showHelp = () => {
  appStore.showToast('帮助中心功能开发中', 'info')
}

const showFeedback = () => {
  appStore.showToast('意见反馈功能开发中', 'info')
}

const showAbout = () => {
  router.push('/about')
}

const checkUpdate = () => {
  appStore.showToast('已是最新版本', 'success')
}

const logout = async () => {
  try {
    await appStore.showDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    })
    
    await userStore.logout()
    appStore.showToast('退出成功', 'success')
    router.replace('/login')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
      appStore.showToast('退出失败', 'error')
    }
  }
}

// 工具方法
const getRoleText = (role) => {
  const roleMap = {
    student: '学生',
    teacher: '教师',
    admin: '管理员',
    counselor: '辅导员'
  }
  return roleMap[role] || '学生'
}

const getLanguageText = (locale) => {
  const languageMap = {
    'zh-CN': '简体中文',
    'en-US': 'English'
  }
  return languageMap[locale] || '简体中文'
}

const getThemeText = (theme) => {
  const themeMap = {
    light: '浅色',
    dark: '深色',
    auto: '跟随系统'
  }
  return themeMap[theme] || '浅色'
}

// 生命周期
onMounted(async () => {
  // 加载用户统计数据
  try {
    await todoStore.fetchTodos(true)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: 60px;
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-lg);
  padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top));
  color: white;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .avatar-section {
      position: relative;
      
      .van-image {
        border: 3px solid rgba(255, 255, 255, 0.3);
      }
      
      .avatar-edit {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 24px;
        height: 24px;
        background: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
        
        .van-icon {
          font-size: 12px;
          color: white;
        }
      }
    }
    
    .user-details {
      flex: 1;
      
      .username {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        margin: 0 0 var(--spacing-xs) 0;
      }
      
      .user-id,
      .user-role {
        font-size: var(--font-size-sm);
        opacity: 0.9;
        margin: 0;
      }
    }
    
    .settings-icon {
      font-size: 20px;
      cursor: pointer;
      
      &:hover {
        opacity: 0.7;
      }
    }
  }
  
  .stats-section {
    display: flex;
    justify-content: space-around;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        margin-bottom: var(--spacing-xs);
      }
      
      .stat-label {
        font-size: var(--font-size-sm);
        opacity: 0.9;
      }
    }
  }
}

.menu-section {
  padding: var(--spacing-md);
  
  .menu-group {
    margin-bottom: var(--spacing-lg);
    
    .group-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-md) var(--spacing-md);
    }
    
    .van-cell-group {
      border-radius: var(--border-radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-light);
    }
  }
  
  .logout-section {
    margin-top: var(--spacing-xl);
  }
}

// 响应式适配
@media (max-width: 375px) {
  .profile-header {
    padding: var(--spacing-md);
    padding-top: calc(var(--spacing-md) + env(safe-area-inset-top));
    
    .user-info {
      gap: var(--spacing-md);
      
      .avatar-section .van-image {
        width: 60px;
        height: 60px;
      }
      
      .user-details .username {
        font-size: var(--font-size-lg);
      }
    }
    
    .stats-section {
      padding: var(--spacing-md);
    }
  }
}
</style>
