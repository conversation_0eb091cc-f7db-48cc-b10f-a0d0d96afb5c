// 待办事项状态
export const TODO_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}

// 待办事项优先级
export const TODO_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
}

// 待办事项分类
export const TODO_CATEGORIES = {
  ACADEMIC: 'academic',        // 学术相关
  ADMINISTRATIVE: 'administrative', // 行政手续
  ACCOMMODATION: 'accommodation',   // 住宿相关
  FINANCIAL: 'financial',      // 财务相关
  HEALTH: 'health',           // 健康体检
  SOCIAL: 'social',           // 社交活动
  OTHER: 'other'              // 其他
}

// 用户角色
export const USER_ROLES = {
  STUDENT: 'student',
  TEACHER: 'teacher',
  ADMIN: 'admin',
  COUNSELOR: 'counselor'
}

// 性别
export const GENDER = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other'
}

// 学历层次
export const EDUCATION_LEVEL = {
  UNDERGRADUATE: 'undergraduate',  // 本科
  GRADUATE: 'graduate',           // 研究生
  DOCTORAL: 'doctoral',           // 博士
  POSTDOC: 'postdoc'             // 博士后
}

// 入学状态
export const ENROLLMENT_STATUS = {
  PENDING: 'pending',           // 待入学
  IN_PROGRESS: 'in_progress',   // 入学中
  COMPLETED: 'completed',       // 已完成
  DEFERRED: 'deferred'          // 延期入学
}

// 通知类型
export const NOTIFICATION_TYPES = {
  TODO_REMINDER: 'todo_reminder',
  TODO_OVERDUE: 'todo_overdue',
  SYSTEM_NOTICE: 'system_notice',
  ANNOUNCEMENT: 'announcement'
}

// 文件类型
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  ARCHIVE: ['zip', 'rar', '7z'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv'],
  AUDIO: ['mp3', 'wav', 'aac']
}

// 文件大小限制 (MB)
export const FILE_SIZE_LIMITS = {
  IMAGE: 5,
  DOCUMENT: 10,
  ARCHIVE: 20,
  VIDEO: 100,
  AUDIO: 20
}

// 日期格式
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY'
}

// 主题色彩
export const THEME_COLORS = {
  PRIMARY: '#1989fa',
  SUCCESS: '#07c160',
  WARNING: '#ff976a',
  DANGER: '#ee0a24',
  INFO: '#1989fa'
}

// 页面路径
export const ROUTES = {
  HOME: '/home',
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  TODO_LIST: '/todo',
  TODO_DETAIL: '/todo/:id',
  PROFILE: '/profile',
  SETTINGS: '/settings'
}

// 本地存储键名
export const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  SETTINGS: 'app_settings',
  THEME: 'theme',
  LOCALE: 'locale'
}

// API 状态码
export const API_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_ERROR: 500
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限访问该资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '请求参数错误',
  SERVER_ERROR: '服务器内部错误',
  UNKNOWN_ERROR: '未知错误'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功',
  REGISTER_SUCCESS: '注册成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  SAVE_SUCCESS: '保存成功',
  SUBMIT_SUCCESS: '提交成功'
}

// 正则表达式
export const REGEX_PATTERNS = {
  PHONE: /^1[3-9]\d{9}$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  ID_CARD: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  STUDENT_ID: /^\d{8,12}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,10}$/,
  ENGLISH_NAME: /^[a-zA-Z\s]{2,50}$/
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PAGE: 1
}

// 缓存时间 (毫秒)
export const CACHE_TIME = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000
}

// 动画持续时间 (毫秒)
export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500
}

// 设备类型
export const DEVICE_TYPES = {
  MOBILE: 'mobile',
  TABLET: 'tablet',
  DESKTOP: 'desktop'
}

// 网络状态
export const NETWORK_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  SLOW: 'slow'
}
