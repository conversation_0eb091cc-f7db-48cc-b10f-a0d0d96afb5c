# 入学待办事项系统 - Python工具依赖

# 数据处理和分析
pandas>=2.0.0
numpy>=1.24.0

# 数据可视化
matplotlib>=3.7.0
seaborn>=0.12.0

# 数据库连接
mysql-connector-python>=8.0.33

# 系统监控
psutil>=5.9.0
requests>=2.31.0

# 任务调度
schedule>=1.2.0

# 密码加密
bcrypt>=4.0.0

# 邮件发送
secure-smtplib>=0.1.1

# 配置文件处理
PyYAML>=6.0

# 日志处理
colorlog>=6.7.0

# 命令行参数解析
argparse>=1.4.0

# 时间处理
python-dateutil>=2.8.0

# HTTP客户端
httpx>=0.24.0

# 数据验证
pydantic>=2.0.0

# 进度条
tqdm>=4.65.0

# 文件操作
pathlib2>=2.3.7

# JSON处理
ujson>=5.8.0

# 异步支持
asyncio>=3.4.3
aiohttp>=3.8.0

# 科学计算
scipy>=1.10.0

# 机器学习 (可选，用于高级分析)
scikit-learn>=1.3.0

# 统计分析
statsmodels>=0.14.0

# 图像处理 (用于生成图表)
Pillow>=10.0.0

# Excel文件处理
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# PDF生成
reportlab>=4.0.0

# 环境变量管理
python-dotenv>=1.0.0

# 类型检查
mypy>=1.5.0

# 代码格式化
black>=23.0.0
isort>=5.12.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 性能分析
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 网络工具
ping3>=4.0.0
speedtest-cli>=2.1.0

# 系统信息
platform>=1.0.8
socket>=1.0.0

# 文件监控
watchdog>=3.0.0

# 压缩解压
zipfile36>=0.1.3

# 正则表达式增强
regex>=2023.8.8

# 缓存
cachetools>=5.3.0

# 并发处理
concurrent-futures>=3.1.1
threading2>=0.1.2

# 数据库迁移工具
alembic>=1.12.0

# API客户端
fastapi>=0.103.0
uvicorn>=0.23.0

# 配置管理
configparser>=5.3.0

# 加密解密
cryptography>=41.0.0

# 文本处理
jieba>=0.42.1
textdistance>=4.6.0

# 时区处理
pytz>=2023.3

# 数据序列化
pickle5>=0.0.12
msgpack>=1.0.0

# 内存数据库 (用于缓存)
redis>=4.6.0

# 消息队列
celery>=5.3.0

# 分布式任务
dask>=2023.8.0

# 数据流处理
kafka-python>=2.0.2

# 监控和指标
prometheus-client>=0.17.0

# 日志聚合
loguru>=0.7.0

# 配置验证
cerberus>=1.3.4

# 数据清洗
ftfy>=6.1.0

# 字符串相似度
fuzzywuzzy>=0.18.0
python-Levenshtein>=0.21.0

# 数据导出
tabulate>=0.9.0

# 命令行美化
rich>=13.5.0
click>=8.1.0

# 进程管理
supervisor>=4.2.0

# 系统服务
systemd-python>=235

# 网络扫描
python-nmap>=0.7.1

# SSL证书检查
ssl-checker>=2.4.0

# 文件同步
rsync>=1.0.0

# 备份工具
duplicity>=0.8.0

# 数据库备份
mysqldump>=1.0.0

# 容器管理
docker>=6.1.0

# 云服务
boto3>=1.28.0  # AWS
azure-storage-blob>=12.17.0  # Azure
google-cloud-storage>=2.10.0  # GCP

# 消息通知
slack-sdk>=3.21.0
telegram-bot-api>=6.7.0
wechat-sdk>=0.6.0

# 数据同步
pymongo>=4.5.0  # MongoDB
elasticsearch>=8.9.0  # Elasticsearch

# 图形界面 (可选)
tkinter>=8.6
PyQt5>=5.15.0

# Web框架 (用于监控面板)
flask>=2.3.0
django>=4.2.0

# 模板引擎
jinja2>=3.1.0

# 静态文件服务
whitenoise>=6.5.0

# 开发工具
ipython>=8.14.0
jupyter>=1.0.0
notebook>=7.0.0
