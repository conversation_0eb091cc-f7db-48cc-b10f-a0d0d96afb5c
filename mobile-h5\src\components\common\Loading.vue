<template>
  <div class="loading-component" :class="loadingClass">
    <!-- 全屏加载 -->
    <van-overlay v-if="type === 'overlay'" :show="show" class="loading-overlay">
      <div class="loading-content">
        <van-loading
          :type="loadingType"
          :size="size"
          :color="color"
          :text-color="textColor"
        >
          {{ text }}
        </van-loading>
      </div>
    </van-overlay>
    
    <!-- 内联加载 -->
    <div v-else-if="type === 'inline'" class="loading-inline">
      <van-loading
        :type="loadingType"
        :size="size"
        :color="color"
        :text-color="textColor"
        :vertical="vertical"
      >
        {{ text }}
      </van-loading>
    </div>
    
    <!-- 骨架屏加载 -->
    <div v-else-if="type === 'skeleton'" class="loading-skeleton">
      <van-skeleton
        :row="skeletonRows"
        :loading="show"
        :animate="skeletonAnimate"
        :round="skeletonRound"
        :row-width="skeletonRowWidth"
        :title="skeletonTitle"
        :avatar="skeletonAvatar"
        :avatar-size="skeletonAvatarSize"
        :avatar-shape="skeletonAvatarShape"
      >
        <slot />
      </van-skeleton>
    </div>
    
    <!-- 自定义加载 -->
    <div v-else-if="type === 'custom'" class="loading-custom">
      <div class="custom-spinner" :style="customSpinnerStyle">
        <div class="spinner-dot" v-for="i in 3" :key="i"></div>
      </div>
      <div v-if="text" class="custom-text">{{ text }}</div>
    </div>
    
    <!-- 脉冲加载 -->
    <div v-else-if="type === 'pulse'" class="loading-pulse">
      <div class="pulse-container">
        <div class="pulse-circle" v-for="i in 3" :key="i"></div>
      </div>
      <div v-if="text" class="pulse-text">{{ text }}</div>
    </div>
    
    <!-- 默认加载 -->
    <van-loading
      v-else
      :type="loadingType"
      :size="size"
      :color="color"
      :text-color="textColor"
      :vertical="vertical"
    >
      {{ text }}
    </van-loading>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  // 显示控制
  show: {
    type: Boolean,
    default: true
  },
  
  // 加载类型
  type: {
    type: String,
    default: 'default', // default, overlay, inline, skeleton, custom, pulse
    validator: (value) => ['default', 'overlay', 'inline', 'skeleton', 'custom', 'pulse'].includes(value)
  },
  
  // 基础属性
  text: {
    type: String,
    default: '加载中...'
  },
  size: {
    type: [String, Number],
    default: '24px'
  },
  color: {
    type: String,
    default: '#1989fa'
  },
  textColor: {
    type: String,
    default: '#969799'
  },
  
  // 样式属性
  loadingType: {
    type: String,
    default: 'circular', // circular, spinner
    validator: (value) => ['circular', 'spinner'].includes(value)
  },
  vertical: {
    type: Boolean,
    default: false
  },
  
  // 骨架屏属性
  skeletonRows: {
    type: Number,
    default: 3
  },
  skeletonAnimate: {
    type: Boolean,
    default: true
  },
  skeletonRound: {
    type: Boolean,
    default: false
  },
  skeletonRowWidth: {
    type: [String, Array],
    default: '100%'
  },
  skeletonTitle: {
    type: Boolean,
    default: false
  },
  skeletonAvatar: {
    type: Boolean,
    default: false
  },
  skeletonAvatarSize: {
    type: [String, Number],
    default: '32px'
  },
  skeletonAvatarShape: {
    type: String,
    default: 'round', // round, square
    validator: (value) => ['round', 'square'].includes(value)
  },
  
  // 自定义样式
  customColor: {
    type: String,
    default: '#1989fa'
  }
})

// 计算属性
const loadingClass = computed(() => {
  return {
    [`loading-${props.type}`]: true,
    'loading-show': props.show
  }
})

const customSpinnerStyle = computed(() => {
  return {
    '--spinner-color': props.customColor,
    '--spinner-size': typeof props.size === 'number' ? `${props.size}px` : props.size
  }
})
</script>

<style lang="scss" scoped>
.loading-component {
  &.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
  }
  
  &.loading-inline {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-lg);
  }
  
  &.loading-skeleton {
    padding: var(--spacing-md);
  }
}

.loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  
  .loading-content {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    backdrop-filter: blur(10px);
  }
}

.loading-inline {
  min-height: 100px;
}

.loading-custom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  
  .custom-spinner {
    display: flex;
    gap: 4px;
    margin-bottom: var(--spacing-md);
    
    .spinner-dot {
      width: 8px;
      height: 8px;
      background: var(--spinner-color, #1989fa);
      border-radius: 50%;
      animation: spinner-bounce 1.4s ease-in-out infinite both;
      
      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
  
  .custom-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}

.loading-pulse {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  
  .pulse-container {
    position: relative;
    width: 40px;
    height: 40px;
    margin-bottom: var(--spacing-md);
    
    .pulse-circle {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid var(--primary-color);
      border-radius: 50%;
      opacity: 0;
      animation: pulse-scale 2s infinite;
      
      &:nth-child(1) {
        animation-delay: 0s;
      }
      
      &:nth-child(2) {
        animation-delay: 0.6s;
      }
      
      &:nth-child(3) {
        animation-delay: 1.2s;
      }
    }
  }
  
  .pulse-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}

// 动画
@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .loading-overlay .loading-content {
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
  }
}
</style>
