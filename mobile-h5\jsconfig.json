{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@views/*": ["src/views/*"], "@stores/*": ["src/stores/*"], "@utils/*": ["src/utils/*"], "@api/*": ["src/api/*"], "@styles/*": ["src/styles/*"], "@assets/*": ["src/assets/*"]}, "types": ["vite/client", "node"]}, "include": ["src/**/*", "src/**/*.vue", "vite.config.js"], "exclude": ["node_modules", "dist", "**/*.d.ts"]}