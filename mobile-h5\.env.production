# 生产环境配置

# 应用标题
VITE_APP_TITLE=入学待办事项系统

# API基础URL
VITE_API_BASE_URL=https://api.yourdomain.com/api

# 应用版本
VITE_APP_VERSION=1.0.0

# 环境标识
VITE_NODE_ENV=production

# 是否开启调试模式
VITE_DEBUG=false

# 是否开启Mock数据
VITE_USE_MOCK=false

# 是否开启PWA
VITE_USE_PWA=true

# 是否开启vconsole
VITE_USE_VCONSOLE=false

# 上传文件大小限制(MB)
VITE_UPLOAD_SIZE_LIMIT=10

# 分页大小
VITE_PAGE_SIZE=20

# Token存储键名
VITE_TOKEN_KEY=token

# 刷新Token存储键名
VITE_REFRESH_TOKEN_KEY=refresh_token

# 语言设置
VITE_DEFAULT_LOCALE=zh-CN

# 主题设置
VITE_DEFAULT_THEME=light

# 是否开启错误监控
VITE_USE_ERROR_MONITOR=true

# 错误监控DSN
VITE_ERROR_MONITOR_DSN=https://<EMAIL>/project-id

# 是否开启性能监控
VITE_USE_PERFORMANCE_MONITOR=true

# CDN基础URL
VITE_CDN_BASE_URL=https://cdn.yourdomain.com

# 静态资源基础URL
VITE_STATIC_BASE_URL=https://static.yourdomain.com/

# WebSocket URL
VITE_WS_URL=wss://api.yourdomain.com/ws

# 地图API密钥
VITE_MAP_API_KEY=your-map-api-key

# 第三方登录配置
VITE_WECHAT_APP_ID=your-wechat-app-id
VITE_QQ_APP_ID=your-qq-app-id
VITE_ALIPAY_APP_ID=your-alipay-app-id

# 推送服务配置
VITE_PUSH_PUBLIC_KEY=your-push-public-key

# 分析工具配置
VITE_GA_ID=your-google-analytics-id
VITE_BAIDU_ANALYTICS_ID=your-baidu-analytics-id
