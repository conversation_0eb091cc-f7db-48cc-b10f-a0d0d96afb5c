# API设计文档

## 📋 概述

本文档定义了入学待办事项系统的RESTful API接口规范，包括认证、用户管理、待办事项管理等核心功能。

## 🔗 基础信息

- **Base URL**: `https://api.enrollment-todo.edu.cn`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时区**: 所有时间均为UTC，前端转换为北京时间显示

## 🔐 认证机制

### JWT Token格式
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": 123,
    "studentId": "**********",
    "role": "student",
    "language": "zh-cn",
    "iat": 1701234567,
    "exp": 1701321567
  }
}
```

### 请求头格式
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept-Language: zh-cn,en-us
```

## 📊 通用响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-12-05T06:30:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "studentId",
        "message": "学号格式不正确"
      }
    ]
  },
  "timestamp": "2024-12-05T06:30:00.000Z"
}
```

## 🔑 认证接口

### 1. 学生登录
```
POST /api/v1/auth/student-login
```

**请求体**:
```json
{
  "studentId": "**********",
  "language": "zh-cn"
}
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 123,
      "studentId": "**********",
      "name": "张三",
      "realName": "张三",
      "role": "student",
      "language": "zh-cn",
      "avatar": null,
      "lastLoginTime": "2024-12-05T06:30:00.000Z"
    },
    "expiresIn": 86400
  }
}
```

### 2. 管理员登录
```
POST /api/v1/auth/admin-login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "password123",
  "language": "zh-cn"
}
```

### 3. 刷新Token
```
POST /api/v1/auth/refresh
```

**请求头**: `Authorization: Bearer <refresh_token>`

### 4. 退出登录
```
POST /api/v1/auth/logout
```

## 👤 用户管理接口

### 1. 获取当前用户信息
```
GET /api/v1/user/profile
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "studentId": "**********",
    "name": "张三",
    "realName": "张三",
    "role": "student",
    "language": "zh-cn",
    "preferences": {
      "theme": "light",
      "notifications": true
    },
    "createdAt": "2024-09-01T00:00:00.000Z",
    "lastLoginTime": "2024-12-05T06:30:00.000Z"
  }
}
```

### 2. 更新用户偏好设置
```
PUT /api/v1/user/preferences
```

**请求体**:
```json
{
  "language": "en-us",
  "theme": "dark",
  "notifications": false
}
```

## 📝 待办事项接口

### 1. 获取待办事项列表
```
GET /api/v1/todos?page=1&limit=20&status=all&category=all
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `status`: 状态筛选 (all|pending|processing|completed)
- `category`: 分类筛选 (all|academic|dormitory|financial|other)
- `search`: 搜索关键词

**响应**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "学费缴纳",
        "description": "请在规定时间内完成学费缴纳",
        "category": "financial",
        "status": "pending",
        "priority": "high",
        "dueDate": "2024-12-15T16:00:00.000Z",
        "createdAt": "2024-12-01T00:00:00.000Z",
        "updatedAt": "2024-12-05T06:30:00.000Z",
        "progress": 0,
        "attachments": [],
        "links": [
          {
            "title": "缴费系统",
            "url": "https://pay.edu.cn"
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. 获取待办事项详情
```
GET /api/v1/todos/:id
```

### 3. 更新待办事项状态
```
PUT /api/v1/todos/:id/status
```

**请求体**:
```json
{
  "status": "completed",
  "note": "已完成缴费",
  "attachments": [
    {
      "name": "payment_receipt.pdf",
      "url": "/uploads/receipts/xxx.pdf",
      "size": 1024000
    }
  ]
}
```

### 4. 上传附件
```
POST /api/v1/todos/:id/attachments
```

**请求**: `multipart/form-data`
- `file`: 文件 (最大10MB)
- `description`: 文件描述

## 📊 统计数据接口

### 1. 获取用户统计数据
```
GET /api/v1/stats/user
```

**响应**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "total": 15,
      "completed": 8,
      "processing": 3,
      "pending": 4,
      "overdue": 1
    },
    "progress": {
      "percentage": 53.33,
      "completedThisWeek": 2,
      "dueThisWeek": 3
    },
    "categories": [
      {
        "category": "academic",
        "total": 6,
        "completed": 4
      },
      {
        "category": "financial",
        "total": 4,
        "completed": 2
      }
    ]
  }
}
```

## 🔧 管理员接口

### 1. 获取所有学生列表
```
GET /api/v1/admin/students?page=1&limit=50&search=
```

### 2. 创建待办模板
```
POST /api/v1/admin/templates
```

**请求体**:
```json
{
  "title": {
    "zh-cn": "学费缴纳",
    "en-us": "Tuition Payment"
  },
  "description": {
    "zh-cn": "请在规定时间内完成学费缴纳",
    "en-us": "Please complete tuition payment within the specified time"
  },
  "category": "financial",
  "priority": "high",
  "estimatedDuration": 30,
  "isActive": true,
  "links": [
    {
      "title": {
        "zh-cn": "缴费系统",
        "en-us": "Payment System"
      },
      "url": "https://pay.edu.cn"
    }
  ]
}
```

### 3. 批量分配待办事项
```
POST /api/v1/admin/todos/batch-assign
```

**请求体**:
```json
{
  "templateId": 123,
  "studentIds": ["**********", "**********"],
  "dueDate": "2024-12-15T16:00:00.000Z",
  "customizations": {
    "priority": "high",
    "note": "请尽快完成"
  }
}
```

## 📱 系统接口

### 1. 获取系统配置
```
GET /api/v1/system/config
```

### 2. 健康检查
```
GET /api/v1/health
```

**响应**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-12-05T06:30:00.000Z",
    "version": "1.0.0",
    "database": {
      "todoSystem": "connected",
      "smartcampus": "connected"
    },
    "uptime": 86400
  }
}
```

## 🚨 错误码定义

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 1000 | 成功 | 200 |
| 4001 | 未授权访问 | 401 |
| 4003 | 权限不足 | 403 |
| 4004 | 资源不存在 | 404 |
| 4009 | 资源冲突 | 409 |
| 4010 | 请求参数错误 | 400 |
| 4011 | 学号不存在 | 400 |
| 4012 | 文件格式不支持 | 400 |
| 4013 | 文件大小超限 | 400 |
| 5001 | 服务器内部错误 | 500 |
| 5002 | 数据库连接错误 | 500 |
| 5003 | 外部服务不可用 | 503 |

## 🔄 API版本控制

- 使用URL路径版本控制: `/api/v1/`
- 向后兼容原则
- 废弃API提前通知机制

## 📝 接口调用示例

### JavaScript/Axios示例
```javascript
// 登录
const loginResponse = await axios.post('/api/v1/auth/student-login', {
  studentId: '**********',
  language: 'zh-cn'
});

// 设置token
const token = loginResponse.data.data.token;
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

// 获取待办列表
const todosResponse = await axios.get('/api/v1/todos', {
  params: {
    page: 1,
    limit: 20,
    status: 'pending'
  }
});
```

## 🧪 测试接口

### 开发环境测试登录
```
POST /api/v1/auth/test-login
```

**请求体**:
```json
{
  "testUser": "student1",
  "language": "zh-cn"
}
```

**注意**: 仅在开发环境可用，生产环境自动禁用。
