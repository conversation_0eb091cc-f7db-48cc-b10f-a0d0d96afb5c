<template>
  <div class="progress-bar-component" :class="progressClass">
    <!-- 进度条标题 -->
    <div v-if="showTitle" class="progress-header">
      <span class="progress-title">{{ title }}</span>
      <span class="progress-value">
        {{ displayValue }}{{ showUnit ? unit : '' }}
      </span>
    </div>
    
    <!-- 进度条主体 -->
    <div class="progress-container">
      <!-- 基础进度条 -->
      <div v-if="type === 'basic'" class="progress-basic">
        <van-progress
          :percentage="percentage"
          :stroke-width="strokeWidth"
          :color="progressColor"
          :track-color="trackColor"
          :show-pivot="showPivot"
          :pivot-text="pivotText"
          :pivot-color="pivotColor"
        />
      </div>
      
      <!-- 圆形进度条 -->
      <div v-else-if="type === 'circle'" class="progress-circle">
        <van-circle
          v-model:current-rate="percentage"
          :rate="percentage"
          :speed="animationSpeed"
          :size="circleSize"
          :color="progressColor"
          :layer-color="trackColor"
          :thickness="strokeWidth"
          :clockwise="clockwise"
          :text="circleText"
        />
      </div>
      
      <!-- 分段进度条 -->
      <div v-else-if="type === 'segment'" class="progress-segment">
        <div class="segment-container">
          <div
            v-for="(segment, index) in segments"
            :key="index"
            class="segment-item"
            :class="{
              'segment-active': index < activeSegments,
              'segment-current': index === activeSegments - 1
            }"
            :style="getSegmentStyle(segment, index)"
          >
            <div class="segment-fill"></div>
            <div v-if="segment.label" class="segment-label">
              {{ segment.label }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 渐变进度条 -->
      <div v-else-if="type === 'gradient'" class="progress-gradient">
        <div class="gradient-track" :style="{ height: strokeWidth + 'px' }">
          <div
            class="gradient-fill"
            :style="{
              width: percentage + '%',
              background: gradientColor,
              height: '100%'
            }"
          ></div>
        </div>
      </div>
      
      <!-- 动画进度条 -->
      <div v-else-if="type === 'animated'" class="progress-animated">
        <div class="animated-track" :style="{ height: strokeWidth + 'px' }">
          <div
            class="animated-fill"
            :style="{
              width: percentage + '%',
              background: progressColor,
              height: '100%'
            }"
          >
            <div class="animated-shine"></div>
          </div>
        </div>
      </div>
      
      <!-- 步骤进度条 -->
      <div v-else-if="type === 'steps'" class="progress-steps">
        <div class="steps-container">
          <div
            v-for="(step, index) in steps"
            :key="index"
            class="step-item"
            :class="{
              'step-finished': index < currentStep,
              'step-active': index === currentStep,
              'step-waiting': index > currentStep
            }"
          >
            <div class="step-icon">
              <van-icon
                v-if="index < currentStep"
                name="success"
                :color="finishedColor"
              />
              <span v-else-if="index === currentStep" class="step-number">
                {{ index + 1 }}
              </span>
              <span v-else class="step-number">{{ index + 1 }}</span>
            </div>
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div v-if="step.description" class="step-description">
                {{ step.description }}
              </div>
            </div>
            <div
              v-if="index < steps.length - 1"
              class="step-line"
              :class="{ 'step-line-finished': index < currentStep }"
            ></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 进度条描述 -->
    <div v-if="description" class="progress-description">
      {{ description }}
    </div>
    
    <!-- 进度条统计 -->
    <div v-if="showStats && stats" class="progress-stats">
      <div class="stat-item" v-for="(stat, key) in stats" :key="key">
        <span class="stat-label">{{ stat.label }}</span>
        <span class="stat-value">{{ stat.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

// Props
const props = defineProps({
  // 基础属性
  type: {
    type: String,
    default: 'basic', // basic, circle, segment, gradient, animated, steps
    validator: (value) => ['basic', 'circle', 'segment', 'gradient', 'animated', 'steps'].includes(value)
  },
  percentage: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  
  // 显示控制
  showTitle: {
    type: Boolean,
    default: true
  },
  showUnit: {
    type: Boolean,
    default: true
  },
  showPivot: {
    type: Boolean,
    default: true
  },
  showStats: {
    type: Boolean,
    default: false
  },
  
  // 文本内容
  title: {
    type: String,
    default: '进度'
  },
  unit: {
    type: String,
    default: '%'
  },
  description: {
    type: String,
    default: ''
  },
  
  // 样式属性
  strokeWidth: {
    type: Number,
    default: 8
  },
  color: {
    type: [String, Array],
    default: '#1989fa'
  },
  trackColor: {
    type: String,
    default: '#e5e5e5'
  },
  
  // 圆形进度条属性
  circleSize: {
    type: Number,
    default: 100
  },
  clockwise: {
    type: Boolean,
    default: true
  },
  animationSpeed: {
    type: Number,
    default: 50
  },
  
  // 分段进度条属性
  segments: {
    type: Array,
    default: () => []
  },
  
  // 步骤进度条属性
  steps: {
    type: Array,
    default: () => []
  },
  currentStep: {
    type: Number,
    default: 0
  },
  finishedColor: {
    type: String,
    default: '#07c160'
  },
  
  // 统计数据
  stats: {
    type: Object,
    default: null
  }
})

// 响应式数据
const animatedPercentage = ref(0)

// 计算属性
const progressClass = computed(() => {
  return {
    [`progress-${props.type}`]: true,
    'progress-animated': props.type === 'animated'
  }
})

const displayValue = computed(() => {
  if (props.type === 'steps') {
    return `${props.currentStep + 1}/${props.steps.length}`
  }
  return Math.round(props.percentage)
})

const progressColor = computed(() => {
  if (Array.isArray(props.color)) {
    return props.color
  }
  
  // 根据进度自动选择颜色
  if (typeof props.color === 'string' && props.color === 'auto') {
    if (props.percentage < 30) return '#ee0a24'
    if (props.percentage < 70) return '#ff976a'
    return '#07c160'
  }
  
  return props.color
})

const gradientColor = computed(() => {
  if (Array.isArray(props.color)) {
    return `linear-gradient(to right, ${props.color.join(', ')})`
  }
  return `linear-gradient(to right, ${props.color}, ${props.color})`
})

const pivotText = computed(() => {
  return `${Math.round(props.percentage)}%`
})

const pivotColor = computed(() => {
  return props.percentage > 50 ? '#fff' : '#1989fa'
})

const circleText = computed(() => {
  return `${Math.round(props.percentage)}%`
})

const activeSegments = computed(() => {
  if (!props.segments.length) return 0
  return Math.ceil((props.percentage / 100) * props.segments.length)
})

// 方法
const getSegmentStyle = (segment, index) => {
  const isActive = index < activeSegments.value
  return {
    backgroundColor: isActive ? (segment.color || props.color) : props.trackColor,
    width: `${100 / props.segments.length}%`
  }
}

// 监听进度变化，添加动画效果
watch(() => props.percentage, (newVal) => {
  if (props.type === 'animated') {
    const duration = 1000
    const startTime = Date.now()
    const startVal = animatedPercentage.value
    const endVal = newVal
    
    const animate = () => {
      const now = Date.now()
      const progress = Math.min((now - startTime) / duration, 1)
      animatedPercentage.value = startVal + (endVal - startVal) * progress
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.progress-bar-component {
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    
    .progress-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
    }
    
    .progress-value {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-bold);
      color: var(--primary-color);
    }
  }
  
  .progress-container {
    margin-bottom: var(--spacing-sm);
  }
  
  .progress-circle {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .progress-segment {
    .segment-container {
      display: flex;
      gap: 4px;
      
      .segment-item {
        position: relative;
        height: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;
        
        &.segment-current {
          animation: pulse 1.5s infinite;
        }
        
        .segment-label {
          position: absolute;
          top: 12px;
          left: 50%;
          transform: translateX(-50%);
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
          white-space: nowrap;
        }
      }
    }
  }
  
  .progress-gradient {
    .gradient-track {
      background: var(--bg-tertiary);
      border-radius: 4px;
      overflow: hidden;
      
      .gradient-fill {
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }
  }
  
  .progress-animated {
    .animated-track {
      background: var(--bg-tertiary);
      border-radius: 4px;
      overflow: hidden;
      
      .animated-fill {
        position: relative;
        border-radius: 4px;
        transition: width 0.3s ease;
        overflow: hidden;
        
        .animated-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
          );
          animation: shine 2s infinite;
        }
      }
    }
  }
  
  .progress-steps {
    .steps-container {
      .step-item {
        position: relative;
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        padding-bottom: var(--spacing-lg);
        
        &:last-child {
          padding-bottom: 0;
          
          .step-line {
            display: none;
          }
        }
        
        .step-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-bold);
          flex-shrink: 0;
          
          .step-number {
            color: white;
          }
        }
        
        .step-content {
          flex: 1;
          padding-top: 4px;
          
          .step-title {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
          }
          
          .step-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: 1.5;
          }
        }
        
        .step-line {
          position: absolute;
          left: 15px;
          top: 32px;
          bottom: 0;
          width: 2px;
          background: var(--border-color);
          
          &.step-line-finished {
            background: var(--success-color);
          }
        }
        
        &.step-finished {
          .step-icon {
            background: var(--success-color);
          }
        }
        
        &.step-active {
          .step-icon {
            background: var(--primary-color);
          }
        }
        
        &.step-waiting {
          .step-icon {
            background: var(--bg-tertiary);
            
            .step-number {
              color: var(--text-tertiary);
            }
          }
          
          .step-content {
            .step-title {
              color: var(--text-tertiary);
            }
          }
        }
      }
    }
  }
  
  .progress-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
  }
  
  .progress-stats {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    
    .stat-item {
      text-align: center;
      
      .stat-label {
        display: block;
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
        margin-bottom: 2px;
      }
      
      .stat-value {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
      }
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
