import { api } from './index'

export const todoAPI = {
  // 获取待办事项列表
  getTodos(params = {}) {
    return api.get('/todos', params)
  },
  
  // 获取待办事项详情
  getTodoById(id) {
    return api.get(`/todos/${id}`)
  },
  
  // 创建待办事项
  createTodo(data) {
    return api.post('/todos', data)
  },
  
  // 更新待办事项
  updateTodo(id, data) {
    return api.put(`/todos/${id}`, data)
  },
  
  // 删除待办事项
  deleteTodo(id) {
    return api.delete(`/todos/${id}`)
  },
  
  // 切换待办事项状态
  toggleTodoStatus(id) {
    return api.patch(`/todos/${id}/toggle`)
  },
  
  // 批量操作待办事项
  batchUpdateTodos(data) {
    return api.post('/todos/batch', data)
  },
  
  // 批量删除待办事项
  batchDeleteTodos(ids) {
    return api.delete('/todos/batch', { data: { ids } })
  },
  
  // 获取待办事项分类
  getCategories() {
    return api.get('/todos/categories')
  },
  
  // 创建分类
  createCategory(data) {
    return api.post('/todos/categories', data)
  },
  
  // 更新分类
  updateCategory(id, data) {
    return api.put(`/todos/categories/${id}`, data)
  },
  
  // 删除分类
  deleteCategory(id) {
    return api.delete(`/todos/categories/${id}`)
  },
  
  // 获取待办事项统计
  getTodoStats(params = {}) {
    return api.get('/todos/stats', params)
  },
  
  // 获取今日待办
  getTodayTodos() {
    return api.get('/todos/today')
  },
  
  // 获取本周待办
  getWeekTodos() {
    return api.get('/todos/week')
  },
  
  // 获取逾期待办
  getOverdueTodos() {
    return api.get('/todos/overdue')
  },
  
  // 获取重要待办
  getImportantTodos() {
    return api.get('/todos/important')
  },
  
  // 搜索待办事项
  searchTodos(query, params = {}) {
    return api.get('/todos/search', { q: query, ...params })
  },
  
  // 添加待办事项评论
  addTodoComment(todoId, data) {
    return api.post(`/todos/${todoId}/comments`, data)
  },
  
  // 获取待办事项评论
  getTodoComments(todoId) {
    return api.get(`/todos/${todoId}/comments`)
  },
  
  // 删除待办事项评论
  deleteTodoComment(todoId, commentId) {
    return api.delete(`/todos/${todoId}/comments/${commentId}`)
  },
  
  // 上传待办事项附件
  uploadTodoAttachment(todoId, formData) {
    return api.upload(`/todos/${todoId}/attachments`, formData)
  },
  
  // 删除待办事项附件
  deleteTodoAttachment(todoId, attachmentId) {
    return api.delete(`/todos/${todoId}/attachments/${attachmentId}`)
  },
  
  // 设置待办事项提醒
  setTodoReminder(todoId, data) {
    return api.post(`/todos/${todoId}/reminder`, data)
  },
  
  // 取消待办事项提醒
  cancelTodoReminder(todoId) {
    return api.delete(`/todos/${todoId}/reminder`)
  },
  
  // 分享待办事项
  shareTodo(todoId, data) {
    return api.post(`/todos/${todoId}/share`, data)
  },
  
  // 复制待办事项
  duplicateTodo(todoId) {
    return api.post(`/todos/${todoId}/duplicate`)
  },
  
  // 归档待办事项
  archiveTodo(todoId) {
    return api.post(`/todos/${todoId}/archive`)
  },
  
  // 取消归档待办事项
  unarchiveTodo(todoId) {
    return api.post(`/todos/${todoId}/unarchive`)
  },
  
  // 获取归档的待办事项
  getArchivedTodos(params = {}) {
    return api.get('/todos/archived', params)
  },
  
  // 导出待办事项
  exportTodos(params = {}) {
    return api.get('/todos/export', params, {
      responseType: 'blob'
    })
  },
  
  // 导入待办事项
  importTodos(formData) {
    return api.upload('/todos/import', formData)
  }
}
