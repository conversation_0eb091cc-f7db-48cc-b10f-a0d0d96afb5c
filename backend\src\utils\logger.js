const winston = require('winston');
const path = require('path');

// 创建日志目录
const logDir = process.env.LOG_PATH || './logs';

// 日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    return log;
  })
);

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { 
    service: 'todo-system-backend',
    pid: process.pid
  },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true
    }),
    
    // 综合日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true
    }),
    
    // 安全日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'security.log'),
      level: 'warn',
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],
  
  // 异常处理
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 10485760,
      maxFiles: 5
    })
  ],
  
  // 拒绝处理
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      maxsize: 10485760,
      maxFiles: 5
    })
  ]
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let log = `${timestamp} ${level}: ${message}`;
        
        // 在开发环境中显示更详细的元数据
        if (Object.keys(meta).length > 0) {
          log += `\n${JSON.stringify(meta, null, 2)}`;
        }
        
        return log;
      })
    )
  }));
}

// 安全事件日志记录器
const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'todo-system-security',
    pid: process.pid
  },
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'security.log'),
      maxsize: 10485760,
      maxFiles: 10
    })
  ]
});

// 性能日志记录器
const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'todo-system-performance',
    pid: process.pid
  },
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'performance.log'),
      maxsize: 10485760,
      maxFiles: 5
    })
  ]
});

// 审计日志记录器
const auditLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'todo-system-audit',
    pid: process.pid
  },
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'audit.log'),
      maxsize: 10485760,
      maxFiles: 20
    })
  ]
});

// 扩展logger方法
logger.security = (message, meta = {}) => {
  securityLogger.info(message, {
    ...meta,
    timestamp: new Date().toISOString(),
    type: 'security_event'
  });
};

logger.performance = (message, meta = {}) => {
  performanceLogger.info(message, {
    ...meta,
    timestamp: new Date().toISOString(),
    type: 'performance_metric'
  });
};

logger.audit = (message, meta = {}) => {
  auditLogger.info(message, {
    ...meta,
    timestamp: new Date().toISOString(),
    type: 'audit_event'
  });
};

// 请求日志记录
logger.request = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    timestamp: new Date().toISOString()
  };
  
  // 根据状态码决定日志级别
  if (res.statusCode >= 500) {
    logger.error('HTTP Request', logData);
  } else if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// 数据库操作日志
logger.database = (operation, table, meta = {}) => {
  logger.info('Database Operation', {
    operation,
    table,
    ...meta,
    timestamp: new Date().toISOString(),
    type: 'database_operation'
  });
};

// 业务操作日志
logger.business = (action, meta = {}) => {
  logger.info('Business Operation', {
    action,
    ...meta,
    timestamp: new Date().toISOString(),
    type: 'business_operation'
  });
};

// 错误日志增强
const originalError = logger.error;
logger.error = (message, meta = {}) => {
  // 如果meta包含error对象，提取堆栈信息
  if (meta.error && meta.error.stack) {
    meta.stack = meta.error.stack;
    meta.errorMessage = meta.error.message;
  }
  
  originalError.call(logger, message, {
    ...meta,
    timestamp: new Date().toISOString()
  });
};

module.exports = logger;
