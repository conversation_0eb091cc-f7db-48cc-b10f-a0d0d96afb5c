-- 入学待办事项系统数据库初始化脚本
-- 创建时间: 2024-12-05
-- 作者: Todo System Team

-- 创建数据库
CREATE DATABASE IF NOT EXISTS school_enrollment_todo 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE school_enrollment_todo;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) UNIQUE NULL COMMENT '学号',
    username VARCHAR(50) UNIQUE NULL COMMENT '用户名(管理员)',
    password_hash VARCHAR(255) NULL COMMENT '密码哈希(管理员)',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    nickname VARCHAR(100) NULL COMMENT '昵称',
    avatar_url VARCHAR(500) NULL COMMENT '头像URL',
    role ENUM('student', 'teacher', 'admin') NOT NULL DEFAULT 'student' COMMENT '角色',
    language VARCHAR(10) DEFAULT 'zh-cn' COMMENT '语言偏好',
    theme VARCHAR(20) DEFAULT 'light' COMMENT '主题偏好',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    last_login_time DATETIME NULL COMMENT '最后登录时间',
    last_sync_time DATETIME NULL COMMENT '最后同步时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_student_id (student_id),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';

-- 待办模板表
CREATE TABLE IF NOT EXISTS todo_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title JSON NOT NULL COMMENT '标题(多语言)',
    description JSON NOT NULL COMMENT '描述(多语言)',
    category ENUM('academic', 'dormitory', 'financial', 'health', 'other') NOT NULL COMMENT '分类',
    priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium' COMMENT '优先级',
    estimated_duration INT DEFAULT 30 COMMENT '预计完成时间(分钟)',
    related_links JSON NULL COMMENT '相关链接',
    required_attachments JSON NULL COMMENT '必需附件类型',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_by INT NOT NULL COMMENT '创建者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category (category),
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='待办模板表';

-- 待办事项表
CREATE TABLE IF NOT EXISTS todo_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    template_id INT NULL COMMENT '模板ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    description TEXT NULL COMMENT '描述',
    category ENUM('academic', 'dormitory', 'financial', 'health', 'other') NOT NULL COMMENT '分类',
    status ENUM('pending', 'processing', 'completed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
    priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium' COMMENT '优先级',
    progress INT DEFAULT 0 COMMENT '进度百分比',
    due_date DATETIME NULL COMMENT '截止时间',
    completed_at DATETIME NULL COMMENT '完成时间',
    notes TEXT NULL COMMENT '备注',
    related_links JSON NULL COMMENT '相关链接',
    metadata JSON NULL COMMENT '元数据',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_template_id (template_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date),
    INDEX idx_created_at (created_at),
    INDEX idx_user_status (user_id, status),
    INDEX idx_user_category (user_id, category),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES todo_templates(id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='待办事项表';

-- 附件表
CREATE TABLE IF NOT EXISTS attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    todo_id INT NOT NULL COMMENT '待办事项ID',
    user_id INT NOT NULL COMMENT '用户ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    description VARCHAR(500) NULL COMMENT '文件描述',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    
    INDEX idx_todo_id (todo_id),
    INDEX idx_user_id (user_id),
    INDEX idx_uploaded_at (uploaded_at),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (todo_id) REFERENCES todo_items(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='附件表';

-- 状态历史表
CREATE TABLE IF NOT EXISTS todo_status_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    todo_id INT NOT NULL COMMENT '待办事项ID',
    user_id INT NOT NULL COMMENT '用户ID',
    old_status ENUM('pending', 'processing', 'completed', 'cancelled') NULL COMMENT '原状态',
    new_status ENUM('pending', 'processing', 'completed', 'cancelled') NOT NULL COMMENT '新状态',
    old_progress INT DEFAULT 0 COMMENT '原进度',
    new_progress INT DEFAULT 0 COMMENT '新进度',
    note TEXT NULL COMMENT '备注',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_todo_id (todo_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (todo_id) REFERENCES todo_items(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='状态历史表';

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    type ENUM('system', 'todo', 'reminder', 'announcement') NOT NULL COMMENT '通知类型',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    related_id INT NULL COMMENT '关联ID',
    is_read TINYINT(1) DEFAULT 0 COMMENT '是否已读',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '优先级',
    expires_at DATETIME NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    read_at DATETIME NULL COMMENT '阅读时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_at (created_at),
    INDEX idx_user_unread (user_id, is_read, is_deleted),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='通知表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(500) NULL COMMENT '描述',
    is_public TINYINT(1) DEFAULT 0 COMMENT '是否公开',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
    resource_id INT NULL COMMENT '资源ID',
    details JSON NULL COMMENT '操作详情',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    result ENUM('success', 'failure') NOT NULL COMMENT '操作结果',
    error_message TEXT NULL COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_resource_id (resource_id),
    INDEX idx_result (result),
    INDEX idx_created_at (created_at),
    INDEX idx_user_action (user_id, action),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='操作日志表';

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, description, is_public) VALUES
('system_name', '{"zh-cn": "入学待办事项系统", "en-us": "Enrollment Todo System"}', '系统名称', 1),
('system_version', '1.0.0', '系统版本', 1),
('default_language', 'zh-cn', '默认语言', 1),
('supported_languages', '["zh-cn", "en-us"]', '支持的语言', 1),
('max_file_size', '10485760', '最大文件上传大小(字节)', 0),
('allowed_file_types', '["image/jpeg", "image/png", "image/gif", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]', '允许的文件类型', 0),
('session_timeout', '3600', '会话超时时间(秒)', 0),
('password_min_length', '6', '密码最小长度', 0),
('enable_email_notifications', 'true', '启用邮件通知', 0),
('enable_sms_notifications', 'false', '启用短信通知', 0),
('maintenance_mode', 'false', '维护模式', 0),
('registration_enabled', 'false', '启用注册', 0);

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, password_hash, real_name, nickname, role, is_active) VALUES
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO8G', '系统管理员', '管理员', 'admin', 1);

-- 插入默认待办模板
INSERT INTO todo_templates (title, description, category, priority, estimated_duration, created_by) VALUES
('{"zh-cn": "学费缴纳", "en-us": "Tuition Payment"}', '{"zh-cn": "请在规定时间内完成学费缴纳，逾期将影响正常入学。", "en-us": "Please complete tuition payment within the specified time."}', 'financial', 'high', 30, 1),
('{"zh-cn": "宿舍分配", "en-us": "Dormitory Assignment"}', '{"zh-cn": "选择宿舍并完成入住手续。", "en-us": "Select dormitory and complete check-in procedures."}', 'dormitory', 'medium', 45, 1),
('{"zh-cn": "体检报告", "en-us": "Medical Examination"}', '{"zh-cn": "提交入学体检报告。", "en-us": "Submit medical examination report."}', 'health', 'high', 60, 1),
('{"zh-cn": "课程选择", "en-us": "Course Selection"}', '{"zh-cn": "选择本学期的课程。", "en-us": "Select courses for this semester."}', 'academic', 'medium', 90, 1),
('{"zh-cn": "学生证办理", "en-us": "Student ID Card"}', '{"zh-cn": "办理学生证件。", "en-us": "Apply for student ID card."}', 'other', 'low', 20, 1);

-- 创建视图：用户统计
CREATE VIEW user_stats AS
SELECT 
    role,
    language,
    COUNT(*) as user_count,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count,
    SUM(CASE WHEN last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as weekly_active_count,
    SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as monthly_new_count
FROM users
GROUP BY role, language;

-- 创建视图：待办统计
CREATE VIEW todo_stats AS
SELECT 
    u.role,
    t.category,
    t.status,
    t.priority,
    COUNT(*) as todo_count,
    AVG(t.progress) as avg_progress,
    AVG(CASE WHEN t.completed_at IS NOT NULL THEN 
        TIMESTAMPDIFF(HOUR, t.created_at, t.completed_at) 
        ELSE NULL END) as avg_completion_hours
FROM todo_items t
JOIN users u ON t.user_id = u.id
GROUP BY u.role, t.category, t.status, t.priority;

-- 创建存储过程：清理过期数据
DELIMITER //
CREATE PROCEDURE CleanupExpiredData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除过期通知
    DELETE FROM notifications 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_deleted = 1;
    
    -- 删除90天前的操作日志
    DELETE FROM operation_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 删除已删除的附件记录（保留30天）
    DELETE FROM attachments 
    WHERE is_deleted = 1 
    AND uploaded_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    COMMIT;
END //
DELIMITER ;

-- 创建事件：定期清理数据
CREATE EVENT IF NOT EXISTS cleanup_expired_data
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL CleanupExpiredData();

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建索引优化查询性能
CREATE INDEX idx_todo_user_status_created ON todo_items(user_id, status, created_at);
CREATE INDEX idx_notification_user_unread_created ON notifications(user_id, is_read, created_at);
CREATE INDEX idx_operation_log_user_created ON operation_logs(user_id, created_at);

COMMIT;
