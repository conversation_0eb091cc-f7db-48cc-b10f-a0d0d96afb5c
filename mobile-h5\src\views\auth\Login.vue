<template>
  <div class="login-page">
    <!-- 头部 -->
    <div class="login-header">
      <div class="logo">
        <div class="logo-icon">📋</div>
        <h1 class="logo-text">入学待办事项系统</h1>
      </div>
      <p class="subtitle">让入学更简单，让生活更有序</p>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <van-form @submit="handleLogin">
        <!-- 学号登录 -->
        <div class="form-content">
          <div class="form-title">
            <h2>学生登录</h2>
            <p>请输入您的学号进行登录</p>
          </div>

          <van-field
            v-model="form.studentId"
            name="studentId"
            label="学号"
            placeholder="请输入学号"
            left-icon="user-o"
            :rules="[
              { required: true, message: '请输入学号' },
              { validator: validateStudentId, message: '请输入正确的学号格式' }
            ]"
            clearable
            maxlength="20"
          />
        </div>

        <!-- 登录按钮 -->
        <div class="form-actions">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
            size="large"
          >
            登录
          </van-button>
        </div>
      </van-form>

      <!-- 帮助信息 -->
      <div class="help-info">
        <div class="help-item">
          <van-icon name="info-o" />
          <span>请使用您的学号登录系统</span>
        </div>
        <div class="help-item">
          <van-icon name="phone-o" />
          <span>如有问题，请联系教务处</span>
        </div>
      </div>

      <!-- 管理员登录入口 -->
      <div class="admin-login">
        <van-button
          type="default"
          size="small"
          @click="showAdminLogin = true"
        >
          管理员登录
        </van-button>
      </div>
    </div>

    <!-- 管理员登录弹窗 -->
    <van-popup
      v-model:show="showAdminLogin"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="admin-login-popup">
        <div class="popup-header">
          <h3>管理员登录</h3>
          <van-icon name="cross" @click="showAdminLogin = false" />
        </div>

        <van-form @submit="handleAdminLogin">
          <van-field
            v-model="adminForm.username"
            name="username"
            label="用户名"
            placeholder="请输入管理员用户名"
            left-icon="manager-o"
            :rules="[{ required: true, message: '请输入用户名' }]"
            clearable
          />

          <van-field
            v-model="adminForm.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            left-icon="lock"
            :rules="[{ required: true, message: '请输入密码' }]"
            clearable
          />

          <div class="popup-actions">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :loading="adminLoading"
              size="large"
            >
              登录
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user'
import { useAppStore } from '../../stores/app'
import { authAPI } from '../../api/auth'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const loading = ref(false)
const adminLoading = ref(false)
const showAdminLogin = ref(false)

// 学生登录表单
const form = reactive({
  studentId: ''
})

// 管理员登录表单
const adminForm = reactive({
  username: '',
  password: ''
})

// 验证学号格式
const validateStudentId = (value) => {
  // 学号格式验证：通常为数字，长度在6-20位之间
  const studentIdPattern = /^[0-9]{6,20}$/
  return studentIdPattern.test(value)
}

// 学生登录方法
const handleLogin = async () => {
  try {
    loading.value = true

    // 使用学号登录
    const credentials = {
      studentId: form.studentId,
      loginType: 'student'
    }

    await userStore.login(credentials)

    appStore.showToast('登录成功', 'success')

    // 跳转到目标页面或首页
    const redirectPath = sessionStorage.getItem('redirectPath') || '/home'
    sessionStorage.removeItem('redirectPath')
    router.replace(redirectPath)

  } catch (error) {
    console.error('学生登录失败:', error)
    appStore.showToast(error.message || '登录失败，请检查学号是否正确', 'error')
  } finally {
    loading.value = false
  }
}

// 管理员登录方法
const handleAdminLogin = async () => {
  try {
    adminLoading.value = true

    const credentials = {
      username: adminForm.username,
      password: adminForm.password,
      loginType: 'admin'
    }

    await userStore.login(credentials)

    appStore.showToast('管理员登录成功', 'success')
    showAdminLogin.value = false

    // 跳转到管理页面
    router.replace('/admin')

  } catch (error) {
    console.error('管理员登录失败:', error)
    appStore.showToast(error.message || '管理员登录失败', 'error')
  } finally {
    adminLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (userStore.isLoggedIn) {
    router.replace('/home')
  }
  
  // 设置页面标题
  document.title = '登录 - 入学待办事项系统'
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding-top: var(--spacing-xxl);
  
  .logo {
    margin-bottom: var(--spacing-md);
    
    .logo-icon {
      font-size: 48px;
      margin-bottom: var(--spacing-sm);
    }
    
    .logo-text {
      color: white;
      font-size: var(--font-size-xxxl);
      font-weight: var(--font-weight-bold);
      margin: 0;
    }
  }
  
  .subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-md);
    margin: 0;
  }
}

.login-form {
  flex: 1;
  background: white;
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-heavy);

  .form-title {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    h2 {
      color: var(--text-primary);
      font-size: var(--font-size-xxl);
      font-weight: var(--font-weight-bold);
      margin: 0 0 var(--spacing-sm) 0;
    }

    p {
      color: var(--text-secondary);
      font-size: var(--font-size-md);
      margin: 0;
    }
  }

  .form-content {
    margin-bottom: var(--spacing-xl);

    .van-field {
      margin-bottom: var(--spacing-lg);

      :deep(.van-field__label) {
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
      }

      :deep(.van-field__control) {
        font-size: var(--font-size-lg);
      }
    }
  }

  .form-actions {
    margin-bottom: var(--spacing-xl);

    .van-button {
      height: 48px;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
    }
  }

  .help-info {
    background: var(--background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);

    .help-item {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-sm);
      color: var(--text-secondary);
      font-size: var(--font-size-sm);

      &:last-child {
        margin-bottom: 0;
      }

      .van-icon {
        margin-right: var(--spacing-sm);
        color: var(--primary-color);
      }
    }
  }

  .admin-login {
    text-align: center;

    .van-button {
      color: var(--text-tertiary);
      border-color: var(--border-color);
    }
  }
}

// 管理员登录弹窗样式
.admin-login-popup {
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);

    h3 {
      margin: 0;
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
    }

    .van-icon {
      font-size: var(--font-size-xl);
      color: var(--text-tertiary);
      cursor: pointer;
    }
  }

  .van-form {
    flex: 1;

    .van-field {
      margin-bottom: var(--spacing-lg);
    }
  }

  .popup-actions {
    margin-top: auto;
    padding-top: var(--spacing-lg);

    .van-button {
      height: 48px;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .login-page {
    padding: var(--spacing-md);
  }
  
  .login-header {
    padding-top: var(--spacing-lg);
    
    .logo-text {
      font-size: var(--font-size-xl);
    }
  }
}
</style>
