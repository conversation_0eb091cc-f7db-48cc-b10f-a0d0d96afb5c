<template>
  <div class="login-page">
    <!-- 头部 -->
    <div class="login-header">
      <div class="logo">
        <div class="logo-icon">📋</div>
        <h1 class="logo-text">入学待办事项系统</h1>
      </div>
      <p class="subtitle">让入学更简单，让生活更有序</p>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <van-form @submit="handleLogin">
        <!-- 登录方式切换 -->
        <van-tabs v-model:active="loginType" class="login-tabs">
          <van-tab title="密码登录" name="password">
            <div class="form-content">
              <van-field
                v-model="form.username"
                name="username"
                label="用户名"
                placeholder="请输入用户名/手机号/邮箱"
                left-icon="user-o"
                :rules="[{ required: true, message: '请输入用户名' }]"
                clearable
              />
              
              <van-field
                v-model="form.password"
                type="password"
                name="password"
                label="密码"
                placeholder="请输入密码"
                left-icon="lock"
                :rules="[{ required: true, message: '请输入密码' }]"
                clearable
              />
            </div>
          </van-tab>
          
          <van-tab title="验证码登录" name="sms">
            <div class="form-content">
              <van-field
                v-model="form.phone"
                name="phone"
                label="手机号"
                placeholder="请输入手机号"
                left-icon="phone-o"
                :rules="[
                  { required: true, message: '请输入手机号' },
                  { validator: validatePhone, message: '请输入正确的手机号' }
                ]"
                clearable
              />
              
              <van-field
                v-model="form.smsCode"
                name="smsCode"
                label="验证码"
                placeholder="请输入验证码"
                left-icon="shield-o"
                :rules="[{ required: true, message: '请输入验证码' }]"
                clearable
              >
                <template #button>
                  <van-button
                    size="small"
                    type="primary"
                    :disabled="!canSendSms || smsCountdown > 0"
                    :loading="sendingSms"
                    @click="sendSmsCode"
                  >
                    {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送验证码' }}
                  </van-button>
                </template>
              </van-field>
            </div>
          </van-tab>
        </van-tabs>

        <!-- 记住我 -->
        <div class="form-options">
          <van-checkbox v-model="form.rememberMe">记住我</van-checkbox>
          <router-link to="/forgot-password" class="forgot-link">
            忘记密码？
          </router-link>
        </div>

        <!-- 登录按钮 -->
        <div class="form-actions">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
            size="large"
          >
            登录
          </van-button>
        </div>
      </van-form>

      <!-- 注册链接 -->
      <div class="register-link">
        <span>还没有账号？</span>
        <router-link to="/register">立即注册</router-link>
      </div>

      <!-- 第三方登录 -->
      <div class="social-login">
        <div class="divider">
          <span>其他登录方式</span>
        </div>
        <div class="social-buttons">
          <van-button
            icon="wechat"
            round
            size="large"
            @click="handleSocialLogin('wechat')"
          >
            微信登录
          </van-button>
          <van-button
            icon="alipay"
            round
            size="large"
            @click="handleSocialLogin('alipay')"
          >
            支付宝登录
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user'
import { useAppStore } from '../../stores/app'
import { validatePhone } from '../../utils/validate'
import { authAPI } from '../../api/auth'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const loginType = ref('password')
const loading = ref(false)
const sendingSms = ref(false)
const smsCountdown = ref(0)

const form = reactive({
  username: '',
  password: '',
  phone: '',
  smsCode: '',
  rememberMe: false
})

// 计算属性
const canSendSms = computed(() => {
  return validatePhone(form.phone)
})

// 方法
const handleLogin = async () => {
  try {
    loading.value = true
    
    let credentials = {}
    
    if (loginType.value === 'password') {
      credentials = {
        username: form.username,
        password: form.password,
        rememberMe: form.rememberMe
      }
    } else {
      credentials = {
        phone: form.phone,
        smsCode: form.smsCode,
        rememberMe: form.rememberMe
      }
    }
    
    await userStore.login(credentials)
    
    appStore.showToast('登录成功', 'success')
    
    // 跳转到目标页面或首页
    const redirectPath = sessionStorage.getItem('redirectPath') || '/home'
    sessionStorage.removeItem('redirectPath')
    router.replace(redirectPath)
    
  } catch (error) {
    console.error('登录失败:', error)
    appStore.showToast(error.message || '登录失败', 'error')
  } finally {
    loading.value = false
  }
}

const sendSmsCode = async () => {
  if (!canSendSms.value || sendingSms.value) return
  
  try {
    sendingSms.value = true
    
    await authAPI.sendSmsCode({
      phone: form.phone,
      type: 'login'
    })
    
    appStore.showToast('验证码已发送', 'success')
    
    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    appStore.showToast(error.message || '发送验证码失败', 'error')
  } finally {
    sendingSms.value = false
  }
}

const handleSocialLogin = (platform) => {
  appStore.showToast(`${platform}登录功能开发中`, 'info')
}

// 生命周期
onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (userStore.isLoggedIn) {
    router.replace('/home')
  }
  
  // 设置页面标题
  document.title = '登录 - 入学待办事项系统'
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding-top: var(--spacing-xxl);
  
  .logo {
    margin-bottom: var(--spacing-md);
    
    .logo-icon {
      font-size: 48px;
      margin-bottom: var(--spacing-sm);
    }
    
    .logo-text {
      color: white;
      font-size: var(--font-size-xxxl);
      font-weight: var(--font-weight-bold);
      margin: 0;
    }
  }
  
  .subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-md);
    margin: 0;
  }
}

.login-form {
  flex: 1;
  background: white;
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-heavy);
  
  .login-tabs {
    margin-bottom: var(--spacing-lg);
    
    :deep(.van-tab) {
      font-weight: var(--font-weight-medium);
    }
  }
  
  .form-content {
    padding: var(--spacing-md) 0;
    
    .van-field {
      margin-bottom: var(--spacing-md);
    }
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    .forgot-link {
      color: var(--primary-color);
      font-size: var(--font-size-sm);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .form-actions {
    margin-bottom: var(--spacing-lg);
  }
  
  .register-link {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    
    a {
      color: var(--primary-color);
      text-decoration: none;
      margin-left: var(--spacing-xs);
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .social-login {
    .divider {
      position: relative;
      text-align: center;
      margin-bottom: var(--spacing-lg);
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--border-color);
      }
      
      span {
        background: white;
        padding: 0 var(--spacing-md);
        color: var(--text-tertiary);
        font-size: var(--font-size-sm);
      }
    }
    
    .social-buttons {
      display: flex;
      gap: var(--spacing-md);
      
      .van-button {
        flex: 1;
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .login-page {
    padding: var(--spacing-md);
  }
  
  .login-header {
    padding-top: var(--spacing-lg);
    
    .logo-text {
      font-size: var(--font-size-xl);
    }
  }
}
</style>
