// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 居中对齐
@mixin center($position: absolute) {
  position: $position;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 垂直居中
@mixin vertical-center($position: absolute) {
  position: $position;
  top: 50%;
  transform: translateY(-50%);
}

// 水平居中
@mixin horizontal-center($position: absolute) {
  position: $position;
  left: 50%;
  transform: translateX(-50%);
}

// Flex 布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// Flex 居中
@mixin flex-center {
  @include flex(row, center, center);
}

// 按钮样式
@mixin button-style($bg-color, $text-color: #fff, $border-color: $bg-color) {
  background-color: $bg-color;
  color: $text-color;
  border: 1px solid $border-color;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card-style($padding: var(--spacing-md), $radius: var(--border-radius-lg)) {
  background-color: var(--bg-primary);
  border-radius: $radius;
  padding: $padding;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
}

// 输入框样式
@mixin input-style {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-normal);
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
  
  &::placeholder {
    color: var(--text-tertiary);
  }
  
  &:disabled {
    background-color: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
  }
}

// 滚动条样式
@mixin scrollbar($width: 6px, $track-color: transparent, $thumb-color: rgba(0, 0, 0, 0.2)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 动画
@mixin animation($name, $duration: 1s, $timing: ease, $delay: 0s, $iteration: 1, $direction: normal, $fill: both) {
  animation: $name $duration $timing $delay $iteration $direction $fill;
}

// 过渡
@mixin transition($property: all, $duration: var(--transition-normal), $timing: ease) {
  transition: $property $duration $timing;
}

// 阴影
@mixin box-shadow($shadow: var(--shadow-light)) {
  box-shadow: $shadow;
}

// 边框
@mixin border($width: 1px, $style: solid, $color: var(--border-color)) {
  border: $width $style $color;
}

// 圆角
@mixin border-radius($radius: var(--border-radius-md)) {
  border-radius: $radius;
}

// 背景渐变
@mixin gradient($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 文本样式
@mixin text-style($size: var(--font-size-md), $weight: var(--font-weight-normal), $color: var(--text-primary), $line-height: var(--line-height-md)) {
  font-size: $size;
  font-weight: $weight;
  color: $color;
  line-height: $line-height;
}

// 隐藏元素
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// 重置列表样式
@mixin reset-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

// 重置按钮样式
@mixin reset-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font: inherit;
  color: inherit;
}

// 1px 边框 (解决移动端 1px 问题)
@mixin hairline($direction: all, $color: var(--border-color)) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    
    @if $direction == top {
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      border-top: 1px solid $color;
      transform-origin: 0 0;
      transform: scaleY(0.5);
    } @else if $direction == bottom {
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      border-bottom: 1px solid $color;
      transform-origin: 0 100%;
      transform: scaleY(0.5);
    } @else if $direction == left {
      top: 0;
      left: 0;
      bottom: 0;
      width: 1px;
      border-left: 1px solid $color;
      transform-origin: 0 0;
      transform: scaleX(0.5);
    } @else if $direction == right {
      top: 0;
      right: 0;
      bottom: 0;
      width: 1px;
      border-right: 1px solid $color;
      transform-origin: 100% 0;
      transform: scaleX(0.5);
    } @else {
      top: 0;
      left: 0;
      width: 200%;
      height: 200%;
      border: 1px solid $color;
      transform-origin: 0 0;
      transform: scale(0.5);
    }
  }
}

// 安全区域适配
@mixin safe-area-inset($property, $direction) {
  #{$property}: constant(safe-area-inset-#{$direction});
  #{$property}: env(safe-area-inset-#{$direction});
}
