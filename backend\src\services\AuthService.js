const bcrypt = require('bcryptjs');
const jwtUtils = require('../utils/jwt');
const logger = require('../utils/logger');
const { ValidationError, UnauthorizedError, ExternalServiceError } = require('../middleware/errorHandler');

/**
 * 认证服务类
 * 处理用户登录、注册、令牌管理等认证相关功能
 */
class AuthService {
  constructor(dbManager) {
    this.dbManager = dbManager;
  }

  /**
   * 学生登录
   * @param {string} studentId - 学号
   * @param {string} language - 语言偏好
   * @returns {Object} 登录结果
   */
  async studentLogin(studentId, language = 'zh-cn') {
    try {
      // 1. 验证学号格式
      if (!this.validateStudentId(studentId)) {
        throw new ValidationError('学号格式不正确');
      }

      // 2. 从smartcampus数据库验证学号并获取学生信息
      const studentInfo = await this.validateStudentWithSmartcampus(studentId);
      if (!studentInfo) {
        throw new ValidationError('学号不存在或无效');
      }

      // 3. 创建或更新本地用户记录
      const localUser = await this.dbManager.createOrUpdateLocalUser(studentInfo);

      // 4. 更新用户语言偏好
      await this.updateUserLanguage(localUser.id, language);

      // 5. 更新最后登录时间
      await this.dbManager.updateLastLoginTime(localUser.id);

      // 6. 生成JWT令牌
      const tokenPair = jwtUtils.generateTokenPair({
        id: localUser.id,
        studentId: localUser.studentId,
        role: localUser.role,
        language
      });

      // 7. 返回登录结果
      return {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        expiresIn: tokenPair.expiresIn,
        user: {
          id: localUser.id,
          studentId: localUser.studentId,
          name: localUser.realName,
          realName: localUser.realName,
          nickname: localUser.nickname,
          role: localUser.role,
          language,
          lastLoginTime: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('Student login failed', {
        studentId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 管理员登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @param {string} language - 语言偏好
   * @returns {Object} 登录结果
   */
  async adminLogin(username, password, language = 'zh-cn') {
    try {
      // 1. 验证用户名和密码格式
      if (!username || !password) {
        throw new ValidationError('用户名和密码不能为空');
      }

      // 2. 查询管理员用户
      const [rows] = await this.dbManager.todoSystemDB.execute(
        `SELECT id, username, password_hash, real_name, role, is_active, last_login_time
         FROM users 
         WHERE username = ? AND role IN ('admin', 'teacher') AND is_active = 1 
         LIMIT 1`,
        [username]
      );

      if (rows.length === 0) {
        throw new UnauthorizedError('用户名或密码错误');
      }

      const user = rows[0];

      // 3. 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      if (!isPasswordValid) {
        throw new UnauthorizedError('用户名或密码错误');
      }

      // 4. 更新用户语言偏好
      await this.updateUserLanguage(user.id, language);

      // 5. 更新最后登录时间
      await this.dbManager.updateLastLoginTime(user.id);

      // 6. 生成JWT令牌
      const tokenPair = jwtUtils.generateTokenPair({
        id: user.id,
        studentId: null, // 管理员没有学号
        role: user.role,
        language
      });

      // 7. 返回登录结果
      return {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        expiresIn: tokenPair.expiresIn,
        user: {
          id: user.id,
          username: user.username,
          name: user.real_name,
          realName: user.real_name,
          role: user.role,
          language,
          lastLoginTime: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('Admin login failed', {
        username,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Object} 新的令牌对
   */
  async refreshToken(refreshToken) {
    try {
      // 1. 验证刷新令牌
      const decoded = jwtUtils.verifyRefreshToken(refreshToken);

      // 2. 获取用户信息
      const user = await this.getUserById(decoded.userId);
      if (!user) {
        throw new UnauthorizedError('用户不存在');
      }

      if (!user.isActive) {
        throw new UnauthorizedError('用户已被禁用');
      }

      // 3. 生成新的令牌对
      const tokenPair = jwtUtils.generateTokenPair({
        id: user.id,
        studentId: user.studentId,
        role: user.role,
        language: user.language
      });

      // 4. 返回新令牌
      return {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        expiresIn: tokenPair.expiresIn,
        user: {
          id: user.id,
          studentId: user.studentId,
          name: user.realName,
          realName: user.realName,
          nickname: user.nickname,
          role: user.role,
          language: user.language
        }
      };
    } catch (error) {
      logger.error('Token refresh failed', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 开发环境测试登录
   * @param {string} testUser - 测试用户标识
   * @param {string} language - 语言偏好
   * @returns {Object} 登录结果
   */
  async testLogin(testUser, language = 'zh-cn') {
    if (process.env.NODE_ENV !== 'development') {
      throw new Error('测试登录仅在开发环境可用');
    }

    try {
      // 预定义的测试用户
      const testUsers = {
        student1: {
          id: 999001,
          studentId: '2024001001',
          realName: '测试学生1',
          nickname: '测试学生1',
          role: 'student'
        },
        student2: {
          id: 999002,
          studentId: '2024001002',
          realName: '测试学生2',
          nickname: '测试学生2',
          role: 'student'
        },
        admin1: {
          id: 999003,
          username: 'testadmin',
          realName: '测试管理员',
          role: 'admin'
        }
      };

      const user = testUsers[testUser];
      if (!user) {
        throw new ValidationError('无效的测试用户');
      }

      // 生成JWT令牌
      const tokenPair = jwtUtils.generateTokenPair({
        id: user.id,
        studentId: user.studentId || null,
        role: user.role,
        language
      });

      return {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        expiresIn: tokenPair.expiresIn,
        user: {
          ...user,
          language,
          lastLoginTime: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('Test login failed', {
        testUser,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 验证学号格式
   * @param {string} studentId - 学号
   * @returns {boolean} 是否有效
   */
  validateStudentId(studentId) {
    if (!studentId || typeof studentId !== 'string') {
      return false;
    }

    // 学号应为8-12位数字
    const pattern = /^[0-9]{8,12}$/;
    return pattern.test(studentId);
  }

  /**
   * 从smartcampus数据库验证学号
   * @param {string} studentId - 学号
   * @returns {Object|null} 学生信息
   */
  async validateStudentWithSmartcampus(studentId) {
    try {
      const studentInfo = await this.dbManager.getStudentInfo(studentId);
      
      if (studentInfo) {
        logger.info('Student validated with smartcampus', {
          studentId,
          studentName: studentInfo.stucName
        });
      } else {
        logger.warn('Student not found in smartcampus', { studentId });
      }

      return studentInfo;
    } catch (error) {
      logger.error('Failed to validate student with smartcampus', {
        studentId,
        error: error.message
      });
      
      // 在smartcampus不可用时，尝试使用本地缓存
      throw new ExternalServiceError('学生信息验证服务暂时不可用');
    }
  }

  /**
   * 根据用户ID获取用户信息
   * @param {number} userId - 用户ID
   * @returns {Object|null} 用户信息
   */
  async getUserById(userId) {
    try {
      const [rows] = await this.dbManager.todoSystemDB.execute(
        `SELECT id, student_id, username, real_name, nickname, role, language, 
                is_active, last_login_time, created_at
         FROM users WHERE id = ? LIMIT 1`,
        [userId]
      );

      if (rows.length === 0) {
        return null;
      }

      const user = rows[0];
      return {
        id: user.id,
        studentId: user.student_id,
        username: user.username,
        realName: user.real_name,
        nickname: user.nickname,
        role: user.role,
        language: user.language || 'zh-cn',
        isActive: user.is_active,
        lastLoginTime: user.last_login_time,
        createdAt: user.created_at
      };
    } catch (error) {
      logger.error('Failed to get user by ID', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 更新用户语言偏好
   * @param {number} userId - 用户ID
   * @param {string} language - 语言代码
   */
  async updateUserLanguage(userId, language) {
    try {
      await this.dbManager.todoSystemDB.execute(
        'UPDATE users SET language = ? WHERE id = ?',
        [language, userId]
      );
    } catch (error) {
      logger.error('Failed to update user language', {
        userId,
        language,
        error: error.message
      });
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 创建管理员用户 (仅用于初始化)
   * @param {Object} adminData - 管理员数据
   * @returns {Object} 创建的用户信息
   */
  async createAdminUser(adminData) {
    const { username, password, realName, role = 'admin' } = adminData;

    try {
      // 检查用户名是否已存在
      const [existingUsers] = await this.dbManager.todoSystemDB.execute(
        'SELECT id FROM users WHERE username = ?',
        [username]
      );

      if (existingUsers.length > 0) {
        throw new ValidationError('用户名已存在');
      }

      // 加密密码
      const passwordHash = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS) || 12);

      // 创建用户
      const [result] = await this.dbManager.todoSystemDB.execute(
        `INSERT INTO users (username, password_hash, real_name, nickname, role, created_at) 
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [username, passwordHash, realName, realName, role]
      );

      logger.audit('Admin user created', {
        userId: result.insertId,
        username,
        role
      });

      return {
        id: result.insertId,
        username,
        realName,
        role
      };
    } catch (error) {
      logger.error('Failed to create admin user', {
        username,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = AuthService;
