#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入学待办事项系统 - 数据分析工具

这个脚本用于分析系统中的用户行为和待办事项完成情况，
生成各种统计报告和可视化图表。

作者: Todo System Team
创建时间: 2024-12-05
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import mysql.connector
from mysql.connector import Error
import argparse
import logging
from typing import Dict, List, Tuple, Optional

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class TodoDataAnalyzer:
    """待办事项数据分析器"""
    
    def __init__(self, db_config: Dict[str, str]):
        """
        初始化数据分析器
        
        Args:
            db_config: 数据库连接配置
        """
        self.db_config = db_config
        self.connection = None
        self.output_dir = 'analysis_output'
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
    def connect_database(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("数据库连接成功")
                return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
        
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def fetch_data(self, query: str) -> pd.DataFrame:
        """
        执行SQL查询并返回DataFrame
        
        Args:
            query: SQL查询语句
            
        Returns:
            pd.DataFrame: 查询结果
        """
        try:
            return pd.read_sql(query, self.connection)
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return pd.DataFrame()
    
    def analyze_user_activity(self) -> Dict:
        """
        分析用户活跃度
        
        Returns:
            Dict: 用户活跃度分析结果
        """
        logger.info("开始分析用户活跃度...")
        
        # 查询用户基本信息
        user_query = """
        SELECT 
            role,
            language,
            DATE(created_at) as register_date,
            DATE(last_login_time) as last_login_date,
            is_active
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        """
        
        users_df = self.fetch_data(user_query)
        
        if users_df.empty:
            logger.warning("没有找到用户数据")
            return {}
        
        # 分析结果
        analysis = {
            'total_users': len(users_df),
            'active_users': len(users_df[users_df['is_active'] == 1]),
            'user_by_role': users_df['role'].value_counts().to_dict(),
            'user_by_language': users_df['language'].value_counts().to_dict(),
            'daily_registrations': users_df.groupby('register_date').size().to_dict()
        }
        
        # 计算活跃度
        if 'last_login_date' in users_df.columns:
            recent_active = users_df[
                users_df['last_login_date'] >= (datetime.now() - timedelta(days=7)).date()
            ]
            analysis['weekly_active_users'] = len(recent_active)
            analysis['weekly_activity_rate'] = len(recent_active) / len(users_df) * 100
        
        logger.info(f"用户活跃度分析完成: {analysis['total_users']} 总用户")
        return analysis
    
    def analyze_todo_completion(self) -> Dict:
        """
        分析待办事项完成情况
        
        Returns:
            Dict: 待办事项完成情况分析结果
        """
        logger.info("开始分析待办事项完成情况...")
        
        # 查询待办事项数据
        todo_query = """
        SELECT 
            t.id,
            t.category,
            t.status,
            t.priority,
            t.progress,
            t.due_date,
            t.completed_at,
            t.created_at,
            DATEDIFF(COALESCE(t.completed_at, NOW()), t.created_at) as completion_days,
            u.role as user_role
        FROM todo_items t
        JOIN users u ON t.user_id = u.id
        WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        """
        
        todos_df = self.fetch_data(todo_query)
        
        if todos_df.empty:
            logger.warning("没有找到待办事项数据")
            return {}
        
        # 分析结果
        analysis = {
            'total_todos': len(todos_df),
            'completed_todos': len(todos_df[todos_df['status'] == 'completed']),
            'completion_rate': len(todos_df[todos_df['status'] == 'completed']) / len(todos_df) * 100,
            'status_distribution': todos_df['status'].value_counts().to_dict(),
            'category_distribution': todos_df['category'].value_counts().to_dict(),
            'priority_distribution': todos_df['priority'].value_counts().to_dict(),
            'avg_completion_days': todos_df[todos_df['status'] == 'completed']['completion_days'].mean()
        }
        
        # 按分类分析完成率
        category_completion = {}
        for category in todos_df['category'].unique():
            category_todos = todos_df[todos_df['category'] == category]
            completed = len(category_todos[category_todos['status'] == 'completed'])
            total = len(category_todos)
            category_completion[category] = {
                'total': total,
                'completed': completed,
                'completion_rate': completed / total * 100 if total > 0 else 0
            }
        
        analysis['category_completion'] = category_completion
        
        logger.info(f"待办事项分析完成: {analysis['completion_rate']:.1f}% 完成率")
        return analysis
    
    def analyze_performance_trends(self) -> Dict:
        """
        分析性能趋势
        
        Returns:
            Dict: 性能趋势分析结果
        """
        logger.info("开始分析性能趋势...")
        
        # 查询每日完成情况
        daily_query = """
        SELECT 
            DATE(completed_at) as completion_date,
            COUNT(*) as completed_count,
            AVG(progress) as avg_progress
        FROM todo_items
        WHERE completed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        AND status = 'completed'
        GROUP BY DATE(completed_at)
        ORDER BY completion_date
        """
        
        daily_df = self.fetch_data(daily_query)
        
        # 查询每日创建情况
        creation_query = """
        SELECT 
            DATE(created_at) as creation_date,
            COUNT(*) as created_count
        FROM todo_items
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY creation_date
        """
        
        creation_df = self.fetch_data(creation_query)
        
        analysis = {
            'daily_completions': daily_df.to_dict('records') if not daily_df.empty else [],
            'daily_creations': creation_df.to_dict('records') if not creation_df.empty else [],
            'avg_daily_completions': daily_df['completed_count'].mean() if not daily_df.empty else 0,
            'avg_daily_creations': creation_df['created_count'].mean() if not creation_df.empty else 0
        }
        
        logger.info("性能趋势分析完成")
        return analysis
    
    def generate_visualizations(self, user_analysis: Dict, todo_analysis: Dict, trend_analysis: Dict):
        """
        生成可视化图表
        
        Args:
            user_analysis: 用户分析结果
            todo_analysis: 待办事项分析结果
            trend_analysis: 趋势分析结果
        """
        logger.info("开始生成可视化图表...")
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('入学待办事项系统数据分析报告', fontsize=16, fontweight='bold')
        
        # 1. 用户角色分布饼图
        if user_analysis.get('user_by_role'):
            axes[0, 0].pie(
                user_analysis['user_by_role'].values(),
                labels=user_analysis['user_by_role'].keys(),
                autopct='%1.1f%%',
                startangle=90
            )
            axes[0, 0].set_title('用户角色分布')
        
        # 2. 待办事项状态分布
        if todo_analysis.get('status_distribution'):
            status_data = todo_analysis['status_distribution']
            axes[0, 1].bar(status_data.keys(), status_data.values())
            axes[0, 1].set_title('待办事项状态分布')
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 分类完成率
        if todo_analysis.get('category_completion'):
            categories = list(todo_analysis['category_completion'].keys())
            completion_rates = [
                todo_analysis['category_completion'][cat]['completion_rate']
                for cat in categories
            ]
            axes[0, 2].bar(categories, completion_rates)
            axes[0, 2].set_title('各分类完成率 (%)')
            axes[0, 2].tick_params(axis='x', rotation=45)
        
        # 4. 优先级分布
        if todo_analysis.get('priority_distribution'):
            priority_data = todo_analysis['priority_distribution']
            axes[1, 0].bar(priority_data.keys(), priority_data.values(), 
                          color=['green', 'yellow', 'orange', 'red'])
            axes[1, 0].set_title('优先级分布')
        
        # 5. 语言偏好分布
        if user_analysis.get('user_by_language'):
            lang_data = user_analysis['user_by_language']
            axes[1, 1].pie(
                lang_data.values(),
                labels=lang_data.keys(),
                autopct='%1.1f%%'
            )
            axes[1, 1].set_title('语言偏好分布')
        
        # 6. 每日完成趋势
        if trend_analysis.get('daily_completions'):
            daily_data = pd.DataFrame(trend_analysis['daily_completions'])
            if not daily_data.empty:
                axes[1, 2].plot(daily_data['completion_date'], daily_data['completed_count'], 
                               marker='o', linewidth=2)
                axes[1, 2].set_title('每日完成趋势')
                axes[1, 2].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.output_dir, f'analysis_charts_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        logger.info(f"图表已保存到: {chart_path}")
        
        plt.show()
    
    def generate_report(self, user_analysis: Dict, todo_analysis: Dict, trend_analysis: Dict) -> str:
        """
        生成分析报告
        
        Args:
            user_analysis: 用户分析结果
            todo_analysis: 待办事项分析结果
            trend_analysis: 趋势分析结果
            
        Returns:
            str: 报告文件路径
        """
        logger.info("开始生成分析报告...")
        
        report = {
            'report_date': datetime.now().isoformat(),
            'summary': {
                'total_users': user_analysis.get('total_users', 0),
                'active_users': user_analysis.get('active_users', 0),
                'total_todos': todo_analysis.get('total_todos', 0),
                'completion_rate': round(todo_analysis.get('completion_rate', 0), 2),
                'avg_completion_days': round(todo_analysis.get('avg_completion_days', 0), 2)
            },
            'user_analysis': user_analysis,
            'todo_analysis': todo_analysis,
            'trend_analysis': trend_analysis
        }
        
        # 保存JSON报告
        report_path = os.path.join(
            self.output_dir, 
            f'analysis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"分析报告已保存到: {report_path}")
        return report_path
    
    def run_full_analysis(self):
        """运行完整的数据分析"""
        logger.info("开始运行完整数据分析...")
        
        if not self.connect_database():
            logger.error("无法连接数据库，分析终止")
            return
        
        try:
            # 执行各项分析
            user_analysis = self.analyze_user_activity()
            todo_analysis = self.analyze_todo_completion()
            trend_analysis = self.analyze_performance_trends()
            
            # 生成可视化图表
            self.generate_visualizations(user_analysis, todo_analysis, trend_analysis)
            
            # 生成报告
            report_path = self.generate_report(user_analysis, todo_analysis, trend_analysis)
            
            logger.info("数据分析完成！")
            logger.info(f"报告文件: {report_path}")
            
        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
        finally:
            self.close_connection()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='入学待办事项系统数据分析工具')
    parser.add_argument('--host', default='localhost', help='数据库主机地址')
    parser.add_argument('--port', type=int, default=3306, help='数据库端口')
    parser.add_argument('--user', default='todo_user', help='数据库用户名')
    parser.add_argument('--password', required=True, help='数据库密码')
    parser.add_argument('--database', default='school_enrollment_todo', help='数据库名称')
    
    args = parser.parse_args()
    
    # 数据库配置
    db_config = {
        'host': args.host,
        'port': args.port,
        'user': args.user,
        'password': args.password,
        'database': args.database,
        'charset': 'utf8mb4'
    }
    
    # 创建分析器并运行分析
    analyzer = TodoDataAnalyzer(db_config)
    analyzer.run_full_analysis()


if __name__ == '__main__':
    main()
