#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
入学待办事项系统 - 系统监控工具

这个脚本用于监控系统的运行状态，包括：
- 服务器性能监控
- 数据库连接监控
- API响应时间监控
- 错误日志分析
- 自动告警功能

作者: Todo System Team
创建时间: 2024-12-05
"""

import os
import sys
import json
import time
import requests
import psutil
import mysql.connector
from mysql.connector import Error
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import argparse
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import threading
import schedule

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_monitor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config: Dict):
        """
        初始化系统监控器
        
        Args:
            config: 监控配置
        """
        self.config = config
        self.alerts = []
        self.metrics_history = []
        self.is_running = False
        
    def check_system_resources(self) -> Dict:
        """
        检查系统资源使用情况
        
        Returns:
            Dict: 系统资源信息
        """
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            
            # 网络IO
            network = psutil.net_io_counters()
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 系统负载 (Linux/Unix)
            try:
                load_avg = os.getloadavg()
            except (OSError, AttributeError):
                load_avg = [0, 0, 0]  # Windows不支持
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': round(memory_available, 2),
                'disk_percent': disk_percent,
                'disk_free_gb': round(disk_free, 2),
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'process_count': process_count,
                'load_avg_1min': load_avg[0],
                'load_avg_5min': load_avg[1],
                'load_avg_15min': load_avg[2]
            }
            
            # 检查阈值并生成告警
            self._check_resource_thresholds(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"检查系统资源失败: {e}")
            return {}
    
    def check_database_connection(self) -> Dict:
        """
        检查数据库连接状态
        
        Returns:
            Dict: 数据库连接信息
        """
        db_status = {
            'timestamp': datetime.now().isoformat(),
            'todo_system_db': {'status': 'unknown', 'response_time': 0},
            'smartcampus_db': {'status': 'unknown', 'response_time': 0}
        }
        
        # 检查待办系统数据库
        try:
            start_time = time.time()
            conn = mysql.connector.connect(
                host=self.config['database']['host'],
                port=self.config['database']['port'],
                user=self.config['database']['user'],
                password=self.config['database']['password'],
                database=self.config['database']['database'],
                connection_timeout=5
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            
            response_time = (time.time() - start_time) * 1000  # ms
            db_status['todo_system_db'] = {
                'status': 'connected',
                'response_time': round(response_time, 2)
            }
            
        except Exception as e:
            logger.error(f"待办系统数据库连接失败: {e}")
            db_status['todo_system_db'] = {
                'status': 'disconnected',
                'response_time': 0,
                'error': str(e)
            }
            self._add_alert('database', 'critical', '待办系统数据库连接失败')
        
        # 检查smartcampus数据库
        try:
            start_time = time.time()
            conn = mysql.connector.connect(
                host=self.config['smartcampus']['host'],
                port=self.config['smartcampus']['port'],
                user=self.config['smartcampus']['user'],
                password=self.config['smartcampus']['password'],
                database=self.config['smartcampus']['database'],
                connection_timeout=5
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            
            response_time = (time.time() - start_time) * 1000  # ms
            db_status['smartcampus_db'] = {
                'status': 'connected',
                'response_time': round(response_time, 2)
            }
            
        except Exception as e:
            logger.warning(f"Smartcampus数据库连接失败: {e}")
            db_status['smartcampus_db'] = {
                'status': 'disconnected',
                'response_time': 0,
                'error': str(e)
            }
            self._add_alert('database', 'warning', 'Smartcampus数据库连接失败')
        
        return db_status
    
    def check_api_endpoints(self) -> Dict:
        """
        检查API端点响应状态
        
        Returns:
            Dict: API端点状态信息
        """
        api_status = {
            'timestamp': datetime.now().isoformat(),
            'endpoints': {}
        }
        
        endpoints = [
            {'name': 'health', 'url': f"{self.config['api']['base_url']}/health"},
            {'name': 'auth', 'url': f"{self.config['api']['base_url']}/api/v1/auth/student-login"},
            {'name': 'todos', 'url': f"{self.config['api']['base_url']}/api/v1/todos"}
        ]
        
        for endpoint in endpoints:
            try:
                start_time = time.time()
                
                if endpoint['name'] == 'health':
                    response = requests.get(endpoint['url'], timeout=10)
                elif endpoint['name'] == 'auth':
                    # 测试登录端点 (应该返回400，因为没有提供参数)
                    response = requests.post(endpoint['url'], json={}, timeout=10)
                else:
                    # 测试需要认证的端点 (应该返回401)
                    response = requests.get(endpoint['url'], timeout=10)
                
                response_time = (time.time() - start_time) * 1000  # ms
                
                # 根据端点类型判断是否正常
                is_healthy = False
                if endpoint['name'] == 'health' and response.status_code == 200:
                    is_healthy = True
                elif endpoint['name'] in ['auth', 'todos'] and response.status_code in [400, 401]:
                    is_healthy = True
                
                api_status['endpoints'][endpoint['name']] = {
                    'status': 'healthy' if is_healthy else 'unhealthy',
                    'status_code': response.status_code,
                    'response_time': round(response_time, 2)
                }
                
                # 检查响应时间阈值
                if response_time > self.config['thresholds']['api_response_time']:
                    self._add_alert(
                        'api', 
                        'warning', 
                        f"API端点 {endpoint['name']} 响应时间过长: {response_time:.2f}ms"
                    )
                
            except Exception as e:
                logger.error(f"检查API端点 {endpoint['name']} 失败: {e}")
                api_status['endpoints'][endpoint['name']] = {
                    'status': 'error',
                    'response_time': 0,
                    'error': str(e)
                }
                self._add_alert('api', 'critical', f"API端点 {endpoint['name']} 不可访问")
        
        return api_status
    
    def analyze_error_logs(self) -> Dict:
        """
        分析错误日志
        
        Returns:
            Dict: 错误日志分析结果
        """
        log_analysis = {
            'timestamp': datetime.now().isoformat(),
            'error_count': 0,
            'warning_count': 0,
            'recent_errors': []
        }
        
        try:
            log_file = self.config['logs']['error_log_path']
            if not os.path.exists(log_file):
                logger.warning(f"错误日志文件不存在: {log_file}")
                return log_analysis
            
            # 读取最近1小时的日志
            one_hour_ago = datetime.now() - timedelta(hours=1)
            
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines[-1000:]:  # 只检查最后1000行
                try:
                    if 'ERROR' in line:
                        log_analysis['error_count'] += 1
                        # 提取时间戳和错误信息
                        if len(log_analysis['recent_errors']) < 10:
                            log_analysis['recent_errors'].append(line.strip())
                    elif 'WARN' in line:
                        log_analysis['warning_count'] += 1
                except Exception:
                    continue
            
            # 检查错误数量阈值
            if log_analysis['error_count'] > self.config['thresholds']['error_count_per_hour']:
                self._add_alert(
                    'logs', 
                    'critical', 
                    f"最近1小时错误数量过多: {log_analysis['error_count']}"
                )
            
        except Exception as e:
            logger.error(f"分析错误日志失败: {e}")
        
        return log_analysis
    
    def _check_resource_thresholds(self, metrics: Dict):
        """
        检查资源使用阈值
        
        Args:
            metrics: 系统资源指标
        """
        thresholds = self.config['thresholds']
        
        # CPU使用率检查
        if metrics['cpu_percent'] > thresholds['cpu_percent']:
            self._add_alert(
                'system', 
                'warning', 
                f"CPU使用率过高: {metrics['cpu_percent']:.1f}%"
            )
        
        # 内存使用率检查
        if metrics['memory_percent'] > thresholds['memory_percent']:
            self._add_alert(
                'system', 
                'warning', 
                f"内存使用率过高: {metrics['memory_percent']:.1f}%"
            )
        
        # 磁盘使用率检查
        if metrics['disk_percent'] > thresholds['disk_percent']:
            self._add_alert(
                'system', 
                'warning', 
                f"磁盘使用率过高: {metrics['disk_percent']:.1f}%"
            )
        
        # 系统负载检查
        if metrics['load_avg_5min'] > thresholds['load_avg']:
            self._add_alert(
                'system', 
                'warning', 
                f"系统负载过高: {metrics['load_avg_5min']:.2f}"
            )
    
    def _add_alert(self, category: str, level: str, message: str):
        """
        添加告警
        
        Args:
            category: 告警类别
            level: 告警级别
            message: 告警消息
        """
        alert = {
            'timestamp': datetime.now().isoformat(),
            'category': category,
            'level': level,
            'message': message
        }
        
        self.alerts.append(alert)
        logger.warning(f"告警: [{level.upper()}] {category} - {message}")
        
        # 发送邮件告警 (如果配置了)
        if level == 'critical' and self.config.get('email'):
            self._send_email_alert(alert)
    
    def _send_email_alert(self, alert: Dict):
        """
        发送邮件告警
        
        Args:
            alert: 告警信息
        """
        try:
            email_config = self.config['email']
            
            msg = MimeMultipart()
            msg['From'] = email_config['from']
            msg['To'] = ', '.join(email_config['to'])
            msg['Subject'] = f"[系统告警] {alert['category']} - {alert['level']}"
            
            body = f"""
            系统监控告警通知
            
            时间: {alert['timestamp']}
            类别: {alert['category']}
            级别: {alert['level']}
            消息: {alert['message']}
            
            请及时处理！
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(email_config['smtp_host'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"告警邮件已发送: {alert['message']}")
            
        except Exception as e:
            logger.error(f"发送告警邮件失败: {e}")
    
    def collect_metrics(self) -> Dict:
        """
        收集所有监控指标
        
        Returns:
            Dict: 完整的监控指标
        """
        logger.info("开始收集监控指标...")
        
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'system_resources': self.check_system_resources(),
            'database_status': self.check_database_connection(),
            'api_status': self.check_api_endpoints(),
            'log_analysis': self.analyze_error_logs(),
            'alerts': self.alerts[-10:]  # 最近10个告警
        }
        
        # 保存到历史记录
        self.metrics_history.append(metrics)
        
        # 只保留最近24小时的数据
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.metrics_history = [
            m for m in self.metrics_history 
            if datetime.fromisoformat(m['timestamp']) > cutoff_time
        ]
        
        return metrics
    
    def save_metrics(self, metrics: Dict):
        """
        保存监控指标到文件
        
        Args:
            metrics: 监控指标
        """
        try:
            output_dir = 'monitoring_data'
            os.makedirs(output_dir, exist_ok=True)
            
            # 按日期保存
            date_str = datetime.now().strftime('%Y%m%d')
            filename = os.path.join(output_dir, f'metrics_{date_str}.jsonl')
            
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(json.dumps(metrics, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"保存监控指标失败: {e}")
    
    def run_monitoring_cycle(self):
        """运行一次监控周期"""
        try:
            # 清空本次告警
            self.alerts = []
            
            # 收集指标
            metrics = self.collect_metrics()
            
            # 保存指标
            self.save_metrics(metrics)
            
            # 输出摘要
            logger.info("监控周期完成")
            logger.info(f"系统CPU: {metrics['system_resources'].get('cpu_percent', 0):.1f}%")
            logger.info(f"系统内存: {metrics['system_resources'].get('memory_percent', 0):.1f}%")
            logger.info(f"数据库状态: {metrics['database_status']['todo_system_db']['status']}")
            logger.info(f"新增告警: {len(self.alerts)}")
            
        except Exception as e:
            logger.error(f"监控周期执行失败: {e}")
    
    def start_monitoring(self, interval: int = 60):
        """
        启动持续监控
        
        Args:
            interval: 监控间隔 (秒)
        """
        logger.info(f"启动系统监控，间隔: {interval}秒")
        self.is_running = True
        
        # 安排定时任务
        schedule.every(interval).seconds.do(self.run_monitoring_cycle)
        
        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭监控...")
            self.is_running = False


def load_config(config_file: str) -> Dict:
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        Dict: 配置信息
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='入学待办事项系统监控工具')
    parser.add_argument('--config', default='monitor_config.json', help='配置文件路径')
    parser.add_argument('--interval', type=int, default=60, help='监控间隔 (秒)')
    parser.add_argument('--once', action='store_true', help='只运行一次监控')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    if not config:
        logger.error("无法加载配置文件，使用默认配置")
        config = {
            'database': {
                'host': 'localhost',
                'port': 3306,
                'user': 'todo_user',
                'password': 'password',
                'database': 'school_enrollment_todo'
            },
            'smartcampus': {
                'host': '**********',
                'port': 3306,
                'user': 'ethanchen',
                'password': 'Spal#2@25',
                'database': 'smartcampus'
            },
            'api': {
                'base_url': 'http://localhost:3001'
            },
            'logs': {
                'error_log_path': './logs/error.log'
            },
            'thresholds': {
                'cpu_percent': 80,
                'memory_percent': 85,
                'disk_percent': 90,
                'load_avg': 2.0,
                'api_response_time': 1000,
                'error_count_per_hour': 10
            }
        }
    
    # 创建监控器
    monitor = SystemMonitor(config)
    
    if args.once:
        # 只运行一次
        monitor.run_monitoring_cycle()
    else:
        # 持续监控
        monitor.start_monitoring(args.interval)


if __name__ == '__main__':
    main()
