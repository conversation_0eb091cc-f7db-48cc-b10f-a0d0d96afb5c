# 入学待办事项系统 - H5移动端

一个专为新生入学设计的待办事项管理系统，帮助学生高效完成入学相关任务。

## ✨ 特性

- 📱 **移动端优化** - 专为移动设备设计的响应式界面
- 🎯 **任务管理** - 完整的待办事项CRUD操作
- 📊 **进度跟踪** - 可视化的任务完成进度
- 🔔 **智能提醒** - 基于截止时间的智能提醒系统
- 🎨 **主题切换** - 支持浅色/深色主题
- 🌍 **国际化** - 支持中英文切换
- 📴 **离线支持** - PWA技术，支持离线使用
- 🔐 **安全认证** - 完整的用户认证和权限管理

## 🛠️ 技术栈

- **框架**: Vue 3 + Vite
- **UI组件**: Vant 4
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: SCSS + CSS Variables
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **PWA**: Vite PWA Plugin
- **构建工具**: Vite

## 📦 项目结构

```
mobile-h5/
├── public/                 # 静态资源
│   ├── icons/             # PWA图标
│   └── manifest.json      # PWA配置
├── src/
│   ├── api/               # API接口
│   ├── assets/            # 静态资源
│   ├── components/        # 组件
│   │   ├── common/        # 通用组件
│   │   └── business/      # 业务组件
│   ├── locales/           # 国际化文件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── vite.config.js         # Vite配置
└── package.json           # 项目配置
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install

# 或使用pnpm
pnpm install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📱 功能模块

### 用户认证
- 登录/注册
- 密码重置
- 第三方登录（微信、支付宝）
- 自动登录

### 待办管理
- 创建/编辑/删除待办
- 任务分类和优先级
- 进度跟踪
- 附件上传
- 评论系统

### 个人中心
- 个人信息管理
- 设置偏好
- 数据统计
- 主题切换

### 系统功能
- 离线缓存
- 推送通知
- 数据同步
- 错误监控

## 🎨 主题定制

系统支持主题定制，可以通过修改CSS变量来自定义主题：

```scss
:root {
  --primary-color: #1989fa;
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  // ... 更多变量
}
```

## 🌍 国际化

支持中英文切换，语言文件位于 `src/locales/` 目录：

- `zh-CN.json` - 中文
- `en-US.json` - 英文

## 📱 PWA支持

应用支持PWA功能：

- 离线访问
- 桌面安装
- 推送通知
- 后台同步

## 🔧 环境配置

### 开发环境变量

在 `.env.development` 中配置开发环境变量：

```env
VITE_API_BASE_URL=http://localhost:8080/api
VITE_USE_MOCK=true
VITE_DEBUG=true
```

### 生产环境变量

在 `.env.production` 中配置生产环境变量：

```env
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_USE_MOCK=false
VITE_DEBUG=false
```

## 🧪 测试

```bash
# 运行单元测试
npm run test

# 运行E2E测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 📦 部署

### 静态部署

构建后将 `dist` 目录部署到静态服务器即可。

### Docker部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### CDN部署

支持将静态资源部署到CDN，在环境变量中配置：

```env
VITE_CDN_BASE_URL=https://cdn.yourdomain.com
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目地址: [GitHub](https://github.com/yourusername/todo-mobile-h5)
- 问题反馈: [Issues](https://github.com/yourusername/todo-mobile-h5/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下开源项目：

- [Vue.js](https://vuejs.org/)
- [Vant](https://vant-contrib.gitee.io/vant/)
- [Vite](https://vitejs.dev/)
- [Pinia](https://pinia.vuejs.org/)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
