// 手机号验证
export function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 邮箱验证
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 密码强度验证
export function validatePassword(password) {
  const rules = {
    minLength: password.length >= 8,
    hasLowerCase: /[a-z]/.test(password),
    hasUpperCase: /[A-Z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
  
  const score = Object.values(rules).filter(Boolean).length
  
  return {
    isValid: score >= 3,
    score,
    rules,
    strength: score <= 2 ? 'weak' : score <= 3 ? 'medium' : 'strong'
  }
}

// 身份证号验证
export function validateIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

// 学号验证
export function validateStudentId(studentId) {
  // 假设学号为8-12位数字
  const studentIdRegex = /^\d{8,12}$/
  return studentIdRegex.test(studentId)
}

// 姓名验证
export function validateName(name) {
  // 中文姓名：2-10个中文字符
  // 英文姓名：2-50个字母和空格
  const chineseNameRegex = /^[\u4e00-\u9fa5]{2,10}$/
  const englishNameRegex = /^[a-zA-Z\s]{2,50}$/
  
  return chineseNameRegex.test(name) || englishNameRegex.test(name)
}

// URL验证
export function validateUrl(url) {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 验证码验证
export function validateCode(code, length = 6) {
  const codeRegex = new RegExp(`^\\d{${length}}$`)
  return codeRegex.test(code)
}

// 表单验证规则
export const validationRules = {
  required: (value) => {
    if (Array.isArray(value)) {
      return value.length > 0
    }
    return value !== null && value !== undefined && value !== ''
  },
  
  minLength: (value, min) => {
    return String(value).length >= min
  },
  
  maxLength: (value, max) => {
    return String(value).length <= max
  },
  
  min: (value, min) => {
    return Number(value) >= min
  },
  
  max: (value, max) => {
    return Number(value) <= max
  },
  
  pattern: (value, pattern) => {
    return new RegExp(pattern).test(value)
  },
  
  email: (value) => {
    return validateEmail(value)
  },
  
  phone: (value) => {
    return validatePhone(value)
  },
  
  idCard: (value) => {
    return validateIdCard(value)
  },
  
  studentId: (value) => {
    return validateStudentId(value)
  },
  
  name: (value) => {
    return validateName(value)
  },
  
  url: (value) => {
    return validateUrl(value)
  },
  
  password: (value) => {
    return validatePassword(value).isValid
  }
}

// 表单验证器
export class FormValidator {
  constructor(rules = {}) {
    this.rules = rules
    this.errors = {}
  }
  
  validate(data) {
    this.errors = {}
    let isValid = true
    
    for (const field in this.rules) {
      const fieldRules = this.rules[field]
      const value = data[field]
      
      for (const rule of fieldRules) {
        const { validator, message, ...params } = rule
        
        let result = true
        
        if (typeof validator === 'string') {
          // 使用预定义的验证规则
          const validatorFn = validationRules[validator]
          if (validatorFn) {
            result = validatorFn(value, params.value)
          }
        } else if (typeof validator === 'function') {
          // 使用自定义验证函数
          result = validator(value, data)
        }
        
        if (!result) {
          this.errors[field] = message
          isValid = false
          break // 一个字段只显示第一个错误
        }
      }
    }
    
    return {
      isValid,
      errors: this.errors
    }
  }
  
  getError(field) {
    return this.errors[field]
  }
  
  hasError(field) {
    return !!this.errors[field]
  }
  
  clearErrors() {
    this.errors = {}
  }
  
  clearError(field) {
    delete this.errors[field]
  }
}

// 常用验证规则配置
export const commonRules = {
  username: [
    { validator: 'required', message: '请输入用户名' },
    { validator: 'minLength', value: 3, message: '用户名至少3个字符' },
    { validator: 'maxLength', value: 20, message: '用户名最多20个字符' }
  ],
  
  password: [
    { validator: 'required', message: '请输入密码' },
    { validator: 'password', message: '密码强度不够，至少包含8位字符，包含大小写字母、数字或特殊字符中的3种' }
  ],
  
  email: [
    { validator: 'required', message: '请输入邮箱' },
    { validator: 'email', message: '请输入正确的邮箱格式' }
  ],
  
  phone: [
    { validator: 'required', message: '请输入手机号' },
    { validator: 'phone', message: '请输入正确的手机号格式' }
  ],
  
  name: [
    { validator: 'required', message: '请输入姓名' },
    { validator: 'name', message: '请输入正确的姓名格式' }
  ],
  
  studentId: [
    { validator: 'required', message: '请输入学号' },
    { validator: 'studentId', message: '请输入正确的学号格式' }
  ],
  
  code: [
    { validator: 'required', message: '请输入验证码' },
    { validator: 'pattern', value: '^\\d{6}$', message: '请输入6位数字验证码' }
  ]
}
