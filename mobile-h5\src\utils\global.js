import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'
import { Toast, Dialog, Notify } from 'vant'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.extend(duration)
dayjs.locale('zh-cn')

// 全局工具函数
export const utils = {
  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD') {
    return dayjs(date).format(format)
  },
  
  // 相对时间
  fromNow(date) {
    return dayjs(date).fromNow()
  },
  
  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },
  
  // 防抖函数
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },
  
  // 节流函数
  throttle(func, limit) {
    let inThrottle
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },
  
  // 深拷贝
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  },
  
  // 生成随机ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  },
  
  // 获取文件扩展名
  getFileExtension(filename) {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
  },
  
  // 检查是否为移动设备
  isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  },
  
  // 检查是否为iOS设备
  isIOS() {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  },
  
  // 检查是否为Android设备
  isAndroid() {
    return /Android/.test(navigator.userAgent)
  },
  
  // 获取设备信息
  getDeviceInfo() {
    const ua = navigator.userAgent
    return {
      isMobile: this.isMobile(),
      isIOS: this.isIOS(),
      isAndroid: this.isAndroid(),
      isWeChat: /MicroMessenger/i.test(ua),
      isAlipay: /AlipayClient/i.test(ua),
      userAgent: ua
    }
  },
  
  // 复制到剪贴板
  async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text)
      Toast.success('复制成功')
      return true
    } catch (err) {
      console.error('复制失败:', err)
      Toast.fail('复制失败')
      return false
    }
  },
  
  // 下载文件
  downloadFile(url, filename) {
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  },
  
  // 格式化数字
  formatNumber(num, precision = 2) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(precision) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(precision) + 'K'
    }
    return num.toString()
  },
  
  // 获取URL参数
  getUrlParams() {
    const params = {}
    const urlSearchParams = new URLSearchParams(window.location.search)
    for (const [key, value] of urlSearchParams) {
      params[key] = value
    }
    return params
  },
  
  // 设置页面标题
  setTitle(title) {
    document.title = title ? `${title} - 入学待办事项系统` : '入学待办事项系统'
  },
  
  // 滚动到顶部
  scrollToTop(smooth = true) {
    window.scrollTo({
      top: 0,
      behavior: smooth ? 'smooth' : 'auto'
    })
  },
  
  // 滚动到元素
  scrollToElement(element, smooth = true) {
    element.scrollIntoView({
      behavior: smooth ? 'smooth' : 'auto',
      block: 'start'
    })
  }
}

// 全局消息提示
export const message = {
  success(text, duration = 2000) {
    Toast.success({
      message: text,
      duration
    })
  },
  
  error(text, duration = 3000) {
    Toast.fail({
      message: text,
      duration
    })
  },
  
  warning(text, duration = 2000) {
    Notify({
      type: 'warning',
      message: text,
      duration
    })
  },
  
  info(text, duration = 2000) {
    Notify({
      type: 'primary',
      message: text,
      duration
    })
  },
  
  loading(text = '加载中...') {
    return Toast.loading({
      message: text,
      forbidClick: true,
      duration: 0
    })
  },
  
  clear() {
    Toast.clear()
  }
}

// 全局对话框
export const dialog = {
  confirm(options) {
    return Dialog.confirm({
      title: '提示',
      ...options
    })
  },
  
  alert(options) {
    return Dialog.alert({
      title: '提示',
      ...options
    })
  }
}

// 设置全局属性
export function setupGlobalProperties(app) {
  app.config.globalProperties.$utils = utils
  app.config.globalProperties.$message = message
  app.config.globalProperties.$dialog = dialog
  app.config.globalProperties.$dayjs = dayjs
  
  // 全局过滤器
  app.config.globalProperties.$filters = {
    formatDate: utils.formatDate,
    fromNow: utils.fromNow,
    formatFileSize: utils.formatFileSize,
    formatNumber: utils.formatNumber
  }
}
