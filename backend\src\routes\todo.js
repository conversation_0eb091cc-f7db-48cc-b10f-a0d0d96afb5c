const express = require('express');
const multer = require('multer');
const path = require('path');
const { body, query, validationResult } = require('express-validator');
const { authenticateToken, requirePermission, checkResourceOwnership } = require('../middleware/auth');
const TodoService = require('../services/TodoService');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 所有待办路由都需要认证
router.use(authenticateToken);

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, process.env.UPLOAD_PATH || './uploads');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.UPLOAD_ALLOWED_TYPES || 'image/jpeg,image/png,application/pdf').split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'), false);
    }
  }
});

/**
 * 获取待办事项列表
 * GET /api/v1/todos
 */
router.get('/',
  requirePermission('todo:read'),
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('status').optional().isIn(['all', 'pending', 'processing', 'completed', 'cancelled']).withMessage('状态参数无效'),
    query('category').optional().isIn(['all', 'academic', 'dormitory', 'financial', 'health', 'other']).withMessage('分类参数无效'),
    query('priority').optional().isIn(['all', 'low', 'medium', 'high', 'urgent']).withMessage('优先级参数无效'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100字符')
  ],
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const userId = req.user.id;
    const filters = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      status: req.query.status || 'all',
      category: req.query.category || 'all',
      priority: req.query.priority || 'all',
      search: req.query.search || ''
    };
    const dbManager = req.app.locals.dbManager;

    try {
      const todoService = new TodoService(dbManager);
      const result = await todoService.getUserTodos(userId, filters);

      res.json({
        success: true,
        code: 1000,
        message: '获取待办列表成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get user todos', {
        userId,
        filters,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 获取待办事项详情
 * GET /api/v1/todos/:id
 */
router.get('/:id',
  requirePermission('todo:read'),
  checkResourceOwnership,
  asyncHandler(async (req, res) => {
    const todoId = parseInt(req.params.id);
    const dbManager = req.app.locals.dbManager;

    try {
      const todoService = new TodoService(dbManager);
      const todo = await todoService.getTodoById(todoId);

      res.json({
        success: true,
        code: 1000,
        message: '获取待办详情成功',
        data: todo,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get todo details', {
        todoId,
        userId: req.user.id,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 更新待办事项状态
 * PUT /api/v1/todos/:id/status
 */
router.put('/:id/status',
  requirePermission('todo:update'),
  checkResourceOwnership,
  [
    body('status')
      .isIn(['pending', 'processing', 'completed', 'cancelled'])
      .withMessage('状态值无效'),
    body('note')
      .optional()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500字符'),
    body('progress')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('进度必须在0-100之间')
  ],
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const todoId = parseInt(req.params.id);
    const { status, note, progress } = req.body;
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    try {
      const todoService = new TodoService(dbManager);
      const updatedTodo = await todoService.updateTodoStatus(todoId, userId, {
        status,
        note,
        progress
      });

      // 记录状态更新日志
      logger.audit('Todo status updated', {
        todoId,
        userId,
        studentId: req.user.studentId,
        oldStatus: updatedTodo.oldStatus,
        newStatus: status,
        progress,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '状态更新成功',
        data: updatedTodo,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to update todo status', {
        todoId,
        userId,
        status,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 上传待办事项附件
 * POST /api/v1/todos/:id/attachments
 */
router.post('/:id/attachments',
  requirePermission('attachment:upload'),
  checkResourceOwnership,
  upload.single('file'),
  [
    body('description')
      .optional()
      .isLength({ max: 200 })
      .withMessage('文件描述长度不能超过200字符')
  ],
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请选择要上传的文件',
        timestamp: new Date().toISOString()
      });
    }

    const todoId = parseInt(req.params.id);
    const { description } = req.body;
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    try {
      const todoService = new TodoService(dbManager);
      const attachment = await todoService.uploadAttachment(todoId, userId, {
        originalName: req.file.originalname,
        storedName: req.file.filename,
        filePath: req.file.path,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        description
      });

      // 记录文件上传日志
      logger.audit('Attachment uploaded', {
        todoId,
        userId,
        studentId: req.user.studentId,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '文件上传成功',
        data: attachment,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to upload attachment', {
        todoId,
        userId,
        fileName: req.file?.originalname,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 删除待办事项附件
 * DELETE /api/v1/todos/:id/attachments/:attachmentId
 */
router.delete('/:id/attachments/:attachmentId',
  requirePermission('attachment:upload'),
  checkResourceOwnership,
  asyncHandler(async (req, res) => {
    const todoId = parseInt(req.params.id);
    const attachmentId = parseInt(req.params.attachmentId);
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    if (!attachmentId || isNaN(attachmentId)) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '附件ID无效',
        timestamp: new Date().toISOString()
      });
    }

    try {
      const todoService = new TodoService(dbManager);
      await todoService.deleteAttachment(todoId, attachmentId, userId);

      // 记录文件删除日志
      logger.audit('Attachment deleted', {
        todoId,
        attachmentId,
        userId,
        studentId: req.user.studentId,
        ip: req.ip
      });

      res.json({
        success: true,
        code: 1000,
        message: '附件删除成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to delete attachment', {
        todoId,
        attachmentId,
        userId,
        error: error.message
      });
      throw error;
    }
  })
);

/**
 * 获取待办事项统计数据
 * GET /api/v1/todos/stats
 */
router.get('/stats',
  requirePermission('todo:read'),
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const dbManager = req.app.locals.dbManager;

    try {
      const todoService = new TodoService(dbManager);
      const stats = await todoService.getTodoStats(userId);

      res.json({
        success: true,
        code: 1000,
        message: '获取统计数据成功',
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to get todo stats', {
        userId,
        error: error.message
      });
      throw error;
    }
  })
);

module.exports = router;
