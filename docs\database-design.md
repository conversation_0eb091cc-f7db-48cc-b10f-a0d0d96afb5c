# 数据库设计文档

## 📋 概述

本文档详细描述了入学待办事项系统的数据库设计，包括双数据库架构、表结构设计、索引策略和数据迁移方案。

## 🏗️ 数据库架构

### 双数据库设计
```
┌─────────────────────┐    ┌─────────────────────┐
│   SmartCampus DB    │    │   Todo System DB    │
│   (**********)     │    │   (localhost)       │
│                     │    │                     │
│ ┌─────────────────┐ │    │ ┌─────────────────┐ │
│ │   stuinfosp     │ │    │ │     users       │ │
│ │   (只读)        │ │◄───┤ │   todo_items    │ │
│ └─────────────────┘ │    │ │   templates     │ │
│                     │    │ │   attachments   │ │
│                     │    │ │   ...           │ │
│                     │    │ └─────────────────┘ │
└─────────────────────┘    └─────────────────────┘
```

### 数据库连接配置
```javascript
// config/database.js
module.exports = {
  // 学生信息数据库（只读）
  smartcampus: {
    host: '**********',
    user: 'ethanchen',
    password: 'Spal#2@25',
    database: 'smartcampus',
    charset: 'utf8mb4',
    timezone: '+08:00',
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    pool: {
      min: 2,
      max: 10,
      acquire: 30000,
      idle: 10000
    }
  },

  // 待办系统数据库（读写）
  todoSystem: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: 'school_enrollment_todo',
    charset: 'utf8mb4',
    timezone: '+08:00',
    pool: {
      min: 5,
      max: 20,
      acquire: 30000,
      idle: 10000
    }
  }
};
```

## 📊 核心表结构设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
  real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
  nickname VARCHAR(100) DEFAULT NULL COMMENT '昵称',
  role ENUM('student', 'admin', 'teacher') DEFAULT 'student' COMMENT '角色',
  language VARCHAR(10) DEFAULT 'zh-cn' COMMENT '语言偏好',
  theme VARCHAR(20) DEFAULT 'light' COMMENT '主题偏好',
  avatar_url VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
  last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后同步时间',
  preferences JSON DEFAULT NULL COMMENT '用户偏好设置',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_student_id (student_id),
  INDEX idx_role (role),
  INDEX idx_last_login (last_login_time),
  INDEX idx_created_at (created_at)
) COMMENT='用户表';
```

### 2. 待办模板表 (todo_templates)
```sql
CREATE TABLE todo_templates (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title JSON NOT NULL COMMENT '标题(多语言)',
  description JSON NOT NULL COMMENT '描述(多语言)',
  category ENUM('academic', 'dormitory', 'financial', 'health', 'other') NOT NULL COMMENT '分类',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
  estimated_duration INT DEFAULT NULL COMMENT '预计完成时间(分钟)',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_by INT NOT NULL COMMENT '创建者ID',
  links JSON DEFAULT NULL COMMENT '相关链接',
  requirements JSON DEFAULT NULL COMMENT '完成要求',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (created_by) REFERENCES users(id),
  INDEX idx_category (category),
  INDEX idx_priority (priority),
  INDEX idx_active (is_active),
  INDEX idx_sort (sort_order)
) COMMENT='待办模板表';
```

### 3. 待办事项表 (todo_items)
```sql
CREATE TABLE todo_items (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  template_id INT DEFAULT NULL COMMENT '模板ID',
  title VARCHAR(200) NOT NULL COMMENT '标题',
  description TEXT DEFAULT NULL COMMENT '描述',
  category ENUM('academic', 'dormitory', 'financial', 'health', 'other') NOT NULL COMMENT '分类',
  status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
  progress INT DEFAULT 0 COMMENT '进度百分比(0-100)',
  due_date TIMESTAMP NULL COMMENT '截止时间',
  completed_at TIMESTAMP NULL COMMENT '完成时间',
  notes TEXT DEFAULT NULL COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (template_id) REFERENCES todo_templates(id) ON DELETE SET NULL,
  INDEX idx_user_status (user_id, status),
  INDEX idx_category (category),
  INDEX idx_priority (priority),
  INDEX idx_due_date (due_date),
  INDEX idx_created_at (created_at),
  INDEX idx_status_due (status, due_date)
) COMMENT='待办事项表';
```

### 4. 附件表 (attachments)
```sql
CREATE TABLE attachments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  todo_id INT NOT NULL COMMENT '待办事项ID',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  description TEXT DEFAULT NULL COMMENT '文件描述',
  uploaded_by INT NOT NULL COMMENT '上传者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (todo_id) REFERENCES todo_items(id) ON DELETE CASCADE,
  FOREIGN KEY (uploaded_by) REFERENCES users(id),
  INDEX idx_todo_id (todo_id),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_created_at (created_at)
) COMMENT='附件表';
```

### 5. 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '操作用户ID',
  todo_id INT DEFAULT NULL COMMENT '相关待办事项ID',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  old_value JSON DEFAULT NULL COMMENT '操作前值',
  new_value JSON DEFAULT NULL COMMENT '操作后值',
  ip_address VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
  user_agent TEXT DEFAULT NULL COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (todo_id) REFERENCES todo_items(id) ON DELETE SET NULL,
  INDEX idx_user_id (user_id),
  INDEX idx_todo_id (todo_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at)
) COMMENT='操作日志表';
```

### 6. 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
  config_value JSON NOT NULL COMMENT '配置值',
  description TEXT DEFAULT NULL COMMENT '配置描述',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(前端可访问)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_key (config_key),
  INDEX idx_public (is_public)
) COMMENT='系统配置表';
```

### 7. 通知表 (notifications)
```sql
CREATE TABLE notifications (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  title VARCHAR(200) NOT NULL COMMENT '通知标题',
  content TEXT NOT NULL COMMENT '通知内容',
  type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info' COMMENT '通知类型',
  is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
  related_todo_id INT DEFAULT NULL COMMENT '相关待办事项ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  read_at TIMESTAMP NULL COMMENT '阅读时间',
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (related_todo_id) REFERENCES todo_items(id) ON DELETE SET NULL,
  INDEX idx_user_unread (user_id, is_read),
  INDEX idx_created_at (created_at)
) COMMENT='通知表';
```

## 🔗 外部数据库表结构

### SmartCampus.stuinfosp (只读)
```sql
-- 学生信息表（外部系统，只读）
-- 主要字段：
-- stuid: 学号 (VARCHAR)
-- stucName: 学生姓名 (VARCHAR)
-- 其他字段根据实际情况调整
```

## 📈 索引策略

### 复合索引设计
```sql
-- 用户待办事项查询优化
CREATE INDEX idx_user_status_due ON todo_items(user_id, status, due_date);

-- 分类统计查询优化
CREATE INDEX idx_user_category_status ON todo_items(user_id, category, status);

-- 时间范围查询优化
CREATE INDEX idx_created_updated ON todo_items(created_at, updated_at);

-- 日志查询优化
CREATE INDEX idx_user_action_time ON operation_logs(user_id, action, created_at);
```

### 分区策略（可选）
```sql
-- 按月分区操作日志表（数据量大时使用）
ALTER TABLE operation_logs 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202412 VALUES LESS THAN (202501),
  PARTITION p202501 VALUES LESS THAN (202502),
  -- 继续添加分区...
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🔄 数据迁移方案

### 1. 初始化脚本
```sql
-- init.sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS school_enrollment_todo 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE school_enrollment_todo;

-- 执行表创建脚本
SOURCE tables/users.sql;
SOURCE tables/todo_templates.sql;
SOURCE tables/todo_items.sql;
SOURCE tables/attachments.sql;
SOURCE tables/operation_logs.sql;
SOURCE tables/system_configs.sql;
SOURCE tables/notifications.sql;

-- 插入初始数据
SOURCE data/initial_configs.sql;
SOURCE data/default_templates.sql;
```

### 2. 学生数据同步策略
```javascript
// 数据同步服务
class StudentSyncService {
  // 全量同步（初始化时）
  async fullSync() {
    const students = await this.smartcampusDB.query(`
      SELECT stuid, stucName FROM stuinfosp 
      WHERE stuid IS NOT NULL AND stucName IS NOT NULL
    `);
    
    for (const student of students) {
      await this.createOrUpdateLocalUser(student);
    }
  }
  
  // 增量同步（定时任务）
  async incrementalSync() {
    // 根据实际情况实现增量同步逻辑
    // 可能需要在smartcampus中添加更新时间字段
  }
  
  async createOrUpdateLocalUser(studentInfo) {
    const existingUser = await this.todoSystemDB.query(`
      SELECT id FROM users WHERE student_id = ?
    `, [studentInfo.stuid]);
    
    if (existingUser.length === 0) {
      // 创建新用户
      await this.todoSystemDB.query(`
        INSERT INTO users (student_id, real_name, nickname, role) 
        VALUES (?, ?, ?, 'student')
      `, [studentInfo.stuid, studentInfo.stucName, studentInfo.stucName]);
    } else {
      // 更新用户信息
      await this.todoSystemDB.query(`
        UPDATE users SET real_name = ?, last_sync_time = NOW() 
        WHERE student_id = ?
      `, [studentInfo.stucName, studentInfo.stuid]);
    }
  }
}
```

### 3. 版本控制和迁移
```javascript
// migrations/001_initial_schema.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建表的迁移脚本
  },
  down: async (queryInterface, Sequelize) => {
    // 回滚脚本
  }
};
```

## 🔧 数据库连接管理

### 连接池配置
```javascript
// database/connection.js
const mysql = require('mysql2/promise');

class DatabaseManager {
  constructor() {
    this.smartcampusPool = mysql.createPool(config.smartcampus);
    this.todoSystemPool = mysql.createPool(config.todoSystem);
  }
  
  // 健康检查
  async healthCheck() {
    try {
      await this.smartcampusPool.execute('SELECT 1');
      await this.todoSystemPool.execute('SELECT 1');
      return { status: 'healthy' };
    } catch (error) {
      return { status: 'unhealthy', error: error.message };
    }
  }
  
  // 优雅关闭
  async close() {
    await this.smartcampusPool.end();
    await this.todoSystemPool.end();
  }
}
```

## 📊 性能优化

### 查询优化
```sql
-- 常用查询的优化版本

-- 1. 用户待办列表查询
SELECT 
  t.id, t.title, t.description, t.category, t.status, 
  t.priority, t.progress, t.due_date, t.created_at
FROM todo_items t
WHERE t.user_id = ? 
  AND t.status IN ('pending', 'processing')
ORDER BY 
  FIELD(t.priority, 'urgent', 'high', 'medium', 'low'),
  t.due_date ASC,
  t.created_at DESC
LIMIT ? OFFSET ?;

-- 2. 统计查询优化
SELECT 
  status,
  COUNT(*) as count,
  category
FROM todo_items 
WHERE user_id = ?
GROUP BY status, category;
```

### 缓存策略
```javascript
// 缓存常用数据
const cacheKeys = {
  userStats: (userId) => `user:stats:${userId}`,
  todoList: (userId, page) => `user:todos:${userId}:${page}`,
  templates: 'templates:active'
};

// Redis缓存配置
const cacheConfig = {
  userStats: 300,      // 5分钟
  todoList: 60,        // 1分钟
  templates: 3600      // 1小时
};
```

## 🛡️ 数据安全

### 敏感数据处理
```sql
-- 创建视图隐藏敏感信息
CREATE VIEW user_public_info AS
SELECT 
  id, student_id, nickname, language, theme, 
  is_active, last_login_time, created_at
FROM users;
```

### 备份策略
```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"

# 备份待办系统数据库
mysqldump -h localhost -u backup_user -p school_enrollment_todo \
  --single-transaction --routines --triggers \
  > $BACKUP_DIR/todo_system_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/todo_system_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```
