const logger = require('../utils/logger');

/**
 * 请求日志中间件
 * 记录所有HTTP请求的详细信息
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // 记录请求开始
  const requestId = generateRequestId();
  req.requestId = requestId;
  
  // 获取客户端IP
  const clientIP = req.ip || 
    req.connection.remoteAddress || 
    req.socket.remoteAddress ||
    (req.connection.socket ? req.connection.socket.remoteAddress : null);

  // 记录请求信息
  const requestInfo = {
    requestId,
    method: req.method,
    url: req.originalUrl,
    ip: clientIP,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    acceptLanguage: req.get('Accept-Language'),
    timestamp: new Date().toISOString()
  };

  // 记录请求体 (排除敏感信息)
  if (req.body && Object.keys(req.body).length > 0) {
    requestInfo.body = sanitizeRequestBody(req.body);
  }

  // 记录查询参数
  if (req.query && Object.keys(req.query).length > 0) {
    requestInfo.query = req.query;
  }

  logger.info('HTTP Request Started', requestInfo);

  // 监听响应完成
  res.on('finish', () => {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    const responseInfo = {
      requestId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.get('Content-Length'),
      userId: req.user?.id,
      studentId: req.user?.studentId,
      timestamp: new Date().toISOString()
    };

    // 根据状态码和响应时间确定日志级别
    let logLevel = 'info';
    if (res.statusCode >= 500) {
      logLevel = 'error';
    } else if (res.statusCode >= 400) {
      logLevel = 'warn';
    } else if (responseTime > 1000) {
      logLevel = 'warn';
      responseInfo.slowRequest = true;
    }

    logger[logLevel]('HTTP Request Completed', responseInfo);

    // 记录性能指标
    if (responseTime > 500) {
      logger.performance('Slow request detected', {
        requestId,
        url: req.originalUrl,
        method: req.method,
        responseTime,
        statusCode: res.statusCode
      });
    }
  });

  // 监听响应错误
  res.on('error', (error) => {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    logger.error('HTTP Request Error', {
      requestId,
      method: req.method,
      url: req.originalUrl,
      error: error.message,
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString()
    });
  });

  next();
};

/**
 * 生成请求ID
 * @returns {string} 唯一的请求ID
 */
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 清理请求体中的敏感信息
 * @param {Object} body - 请求体
 * @returns {Object} 清理后的请求体
 */
function sanitizeRequestBody(body) {
  const sensitiveFields = [
    'password',
    'token',
    'secret',
    'key',
    'authorization',
    'auth',
    'credential'
  ];

  const sanitized = { ...body };

  // 递归清理敏感字段
  function sanitizeObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        result[key] = '[REDACTED]';
      } else if (typeof value === 'object') {
        result[key] = sanitizeObject(value);
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }

  return sanitizeObject(sanitized);
}

/**
 * API访问统计中间件
 * 统计API调用次数和频率
 */
const apiStatsLogger = (req, res, next) => {
  const endpoint = `${req.method} ${req.route?.path || req.originalUrl}`;
  
  res.on('finish', () => {
    logger.audit('API Access', {
      endpoint,
      statusCode: res.statusCode,
      userId: req.user?.id,
      studentId: req.user?.studentId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });
  });

  next();
};

/**
 * 安全事件日志中间件
 * 记录可疑的安全相关活动
 */
const securityLogger = (req, res, next) => {
  // 检测可疑的用户代理
  const userAgent = req.get('User-Agent') || '';
  const suspiciousUserAgents = [
    /sqlmap/i,
    /nikto/i,
    /nessus/i,
    /burp/i,
    /nmap/i,
    /masscan/i
  ];

  if (suspiciousUserAgents.some(pattern => pattern.test(userAgent))) {
    logger.security('Suspicious user agent detected', {
      userAgent,
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }

  // 检测可疑的请求模式
  const suspiciousPatterns = [
    /union.*select/i,
    /<script.*>/i,
    /\.\.\/.*etc\/passwd/i,
    /eval\s*\(/i,
    /base64_decode/i,
    /system\s*\(/i
  ];

  const requestData = JSON.stringify({
    url: req.originalUrl,
    body: req.body,
    query: req.query
  });

  if (suspiciousPatterns.some(pattern => pattern.test(requestData))) {
    logger.security('Suspicious request pattern detected', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
      userAgent,
      pattern: 'potential_injection_attempt',
      timestamp: new Date().toISOString()
    });
  }

  // 检测异常高频请求
  const clientIP = req.ip;
  if (!req.app.locals.requestCounts) {
    req.app.locals.requestCounts = new Map();
  }

  const now = Date.now();
  const windowSize = 60000; // 1分钟窗口
  const maxRequests = 100; // 每分钟最大请求数

  if (!req.app.locals.requestCounts.has(clientIP)) {
    req.app.locals.requestCounts.set(clientIP, []);
  }

  const requests = req.app.locals.requestCounts.get(clientIP);
  
  // 清理过期的请求记录
  const validRequests = requests.filter(timestamp => now - timestamp < windowSize);
  validRequests.push(now);
  req.app.locals.requestCounts.set(clientIP, validRequests);

  if (validRequests.length > maxRequests) {
    logger.security('High frequency requests detected', {
      ip: clientIP,
      requestCount: validRequests.length,
      windowSize: `${windowSize / 1000}s`,
      userAgent,
      timestamp: new Date().toISOString()
    });
  }

  next();
};

module.exports = {
  requestLogger,
  apiStatsLogger,
  securityLogger
};
