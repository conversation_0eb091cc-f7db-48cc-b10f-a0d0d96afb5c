# 安全设计文档

## 📋 概述

本文档详细描述了入学待办事项系统的安全架构设计，包括认证授权、数据保护、网络安全等方面的实施方案。

## 🔐 认证授权架构

### JWT Token设计

#### Token结构
```javascript
// JWT Header
{
  "alg": "HS256",
  "typ": "JWT"
}

// JWT Payload
{
  "userId": 123,
  "studentId": "2024001001",
  "role": "student",
  "language": "zh-cn",
  "permissions": ["todo:read", "todo:update"],
  "iat": 1701234567,    // 签发时间
  "exp": 1701321567,    // 过期时间
  "jti": "uuid-v4"      // Token唯一标识
}

// JWT Signature
HMACSHA256(
  base64UrlEncode(header) + "." + base64UrlEncode(payload),
  secret
)
```

#### Token管理策略
```javascript
// config/jwt.js
module.exports = {
  // 访问令牌配置
  accessToken: {
    secret: process.env.JWT_ACCESS_SECRET,
    expiresIn: '15m',           // 15分钟
    algorithm: 'HS256'
  },
  
  // 刷新令牌配置
  refreshToken: {
    secret: process.env.JWT_REFRESH_SECRET,
    expiresIn: '7d',            // 7天
    algorithm: 'HS256'
  },
  
  // Token黑名单Redis配置
  blacklist: {
    prefix: 'jwt:blacklist:',
    ttl: 86400 * 7             // 7天
  }
};
```

### 权限控制模型 (RBAC)

#### 角色定义
```javascript
// 角色权限映射
const rolePermissions = {
  student: [
    'todo:read',
    'todo:update',
    'attachment:upload',
    'profile:read',
    'profile:update'
  ],
  
  teacher: [
    'todo:read',
    'todo:create',
    'todo:update',
    'student:read',
    'template:read'
  ],
  
  admin: [
    '*'  // 所有权限
  ]
};

// 权限检查中间件
const checkPermission = (permission) => {
  return async (req, res, next) => {
    const userPermissions = rolePermissions[req.user.role] || [];
    
    if (userPermissions.includes('*') || userPermissions.includes(permission)) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        code: 4003,
        message: '权限不足'
      });
    }
  };
};
```

#### 资源访问控制
```javascript
// 资源所有权检查
const checkResourceOwnership = async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.userId;
  
  try {
    const todo = await TodoItem.findById(id);
    
    if (!todo) {
      return res.status(404).json({
        success: false,
        code: 4004,
        message: '资源不存在'
      });
    }
    
    // 检查资源所有权
    if (todo.userId !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        code: 4003,
        message: '无权访问此资源'
      });
    }
    
    req.todo = todo;
    next();
  } catch (error) {
    next(error);
  }
};
```

## 🛡️ 输入验证与防护

### 参数验证
```javascript
// validation/schemas.js
const Joi = require('joi');

const schemas = {
  // 学生登录验证
  studentLogin: Joi.object({
    studentId: Joi.string()
      .pattern(/^[0-9]{8,12}$/)
      .required()
      .messages({
        'string.pattern.base': '学号格式不正确',
        'any.required': '学号不能为空'
      }),
    language: Joi.string()
      .valid('zh-cn', 'en-us')
      .default('zh-cn')
  }),
  
  // 待办事项创建验证
  createTodo: Joi.object({
    title: Joi.string()
      .min(1)
      .max(200)
      .required()
      .messages({
        'string.min': '标题不能为空',
        'string.max': '标题长度不能超过200字符'
      }),
    description: Joi.string()
      .max(2000)
      .allow(''),
    category: Joi.string()
      .valid('academic', 'dormitory', 'financial', 'health', 'other')
      .required(),
    priority: Joi.string()
      .valid('low', 'medium', 'high', 'urgent')
      .default('medium'),
    dueDate: Joi.date()
      .iso()
      .min('now')
      .allow(null)
  }),
  
  // 文件上传验证
  fileUpload: Joi.object({
    file: Joi.object({
      mimetype: Joi.string()
        .valid(
          'image/jpeg', 'image/png', 'image/gif',
          'application/pdf', 'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        .required()
        .messages({
          'any.only': '不支持的文件格式'
        }),
      size: Joi.number()
        .max(10 * 1024 * 1024)  // 10MB
        .required()
        .messages({
          'number.max': '文件大小不能超过10MB'
        })
    }).required()
  })
};

// 验证中间件
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        }
      });
    }
    
    req.validatedData = value;
    next();
  };
};
```

### SQL注入防护
```javascript
// 使用参数化查询
const getUserTodos = async (userId, filters) => {
  const { status, category, search } = filters;
  
  let query = `
    SELECT id, title, description, category, status, priority, due_date
    FROM todo_items 
    WHERE user_id = ?
  `;
  
  const params = [userId];
  
  if (status && status !== 'all') {
    query += ' AND status = ?';
    params.push(status);
  }
  
  if (category && category !== 'all') {
    query += ' AND category = ?';
    params.push(category);
  }
  
  if (search) {
    query += ' AND (title LIKE ? OR description LIKE ?)';
    params.push(`%${search}%`, `%${search}%`);
  }
  
  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(filters.limit, filters.offset);
  
  return await db.execute(query, params);
};
```

### XSS防护
```javascript
// 输出编码
const xss = require('xss');

const sanitizeOutput = (data) => {
  if (typeof data === 'string') {
    return xss(data, {
      whiteList: {
        a: ['href', 'title'],
        b: [],
        i: [],
        strong: [],
        em: [],
        p: [],
        br: []
      }
    });
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeOutput);
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeOutput(value);
    }
    return sanitized;
  }
  
  return data;
};

// 响应中间件
const sanitizeResponse = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    if (data && data.data) {
      data.data = sanitizeOutput(data.data);
    }
    return originalJson.call(this, data);
  };
  
  next();
};
```

## 🔒 数据保护

### 敏感数据加密
```javascript
// 加密工具
const crypto = require('crypto');

class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.secretKey = crypto.scryptSync(process.env.ENCRYPTION_KEY, 'salt', 32);
  }
  
  // 加密敏感数据
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  // 解密敏感数据
  decrypt(encryptedData) {
    const { encrypted, iv, authTag } = encryptedData;
    
    const decipher = crypto.createDecipher(
      this.algorithm, 
      this.secretKey, 
      Buffer.from(iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### 数据库连接安全
```javascript
// 数据库连接安全配置
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  
  // SSL配置
  ssl: {
    ca: fs.readFileSync(path.join(__dirname, 'certs/ca-cert.pem')),
    cert: fs.readFileSync(path.join(__dirname, 'certs/client-cert.pem')),
    key: fs.readFileSync(path.join(__dirname, 'certs/client-key.pem')),
    rejectUnauthorized: true
  },
  
  // 连接池安全配置
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200
  }
};
```

## 🌐 网络安全

### HTTPS配置
```javascript
// server.js
const https = require('https');
const fs = require('fs');

const httpsOptions = {
  key: fs.readFileSync(path.join(__dirname, 'certs/private-key.pem')),
  cert: fs.readFileSync(path.join(__dirname, 'certs/certificate.pem')),
  
  // 安全配置
  secureProtocol: 'TLSv1_2_method',
  ciphers: [
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'ECDHE-RSA-AES256-SHA384'
  ].join(':'),
  honorCipherOrder: true
};

const server = https.createServer(httpsOptions, app);
```

### 安全头配置
```javascript
// 安全中间件
const helmet = require('helmet');

app.use(helmet({
  // 内容安全策略
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.enrollment-todo.edu.cn"]
    }
  },
  
  // HSTS
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  
  // 其他安全头
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true
}));

// CORS配置
const cors = require('cors');
app.use(cors({
  origin: [
    'https://student.enrollment-todo.edu.cn',
    'https://admin.enrollment-todo.edu.cn'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language']
}));
```

### 速率限制
```javascript
// 速率限制配置
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');

// 通用速率限制
const generalLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient
  }),
  windowMs: 15 * 60 * 1000,  // 15分钟
  max: 100,                   // 最多100次请求
  message: {
    success: false,
    code: 4029,
    message: '请求过于频繁，请稍后再试'
  }
});

// 登录速率限制
const loginLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient
  }),
  windowMs: 15 * 60 * 1000,  // 15分钟
  max: 5,                     // 最多5次登录尝试
  skipSuccessfulRequests: true,
  message: {
    success: false,
    code: 4029,
    message: '登录尝试过于频繁，请15分钟后再试'
  }
});

// 文件上传速率限制
const uploadLimiter = rateLimit({
  store: new RedisStore({
    client: redisClient
  }),
  windowMs: 60 * 60 * 1000,  // 1小时
  max: 20,                    // 最多20次上传
  message: {
    success: false,
    code: 4029,
    message: '文件上传过于频繁，请稍后再试'
  }
});
```

## 🔍 安全监控

### 安全日志记录
```javascript
// 安全事件日志
const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: 'logs/security.log',
      maxsize: 10485760,  // 10MB
      maxFiles: 10
    })
  ]
});

// 安全事件记录中间件
const logSecurityEvent = (eventType) => {
  return (req, res, next) => {
    const securityEvent = {
      type: eventType,
      userId: req.user?.userId,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      url: req.originalUrl,
      method: req.method
    };
    
    securityLogger.info('Security Event', securityEvent);
    next();
  };
};

// 使用示例
app.post('/api/v1/auth/student-login', 
  logSecurityEvent('LOGIN_ATTEMPT'),
  loginLimiter,
  validate(schemas.studentLogin),
  authController.studentLogin
);
```

### 异常检测
```javascript
// 异常行为检测
class SecurityMonitor {
  constructor() {
    this.suspiciousPatterns = [
      /union.*select/i,           // SQL注入尝试
      /<script.*>/i,              // XSS尝试
      /\.\.\/.*etc\/passwd/i,     // 路径遍历尝试
      /eval\s*\(/i               // 代码注入尝试
    ];
  }
  
  // 检测可疑请求
  detectSuspiciousRequest(req) {
    const suspicious = [];
    
    // 检查URL
    if (this.containsSuspiciousPattern(req.originalUrl)) {
      suspicious.push('SUSPICIOUS_URL');
    }
    
    // 检查请求体
    if (req.body && this.containsSuspiciousPattern(JSON.stringify(req.body))) {
      suspicious.push('SUSPICIOUS_PAYLOAD');
    }
    
    // 检查请求头
    const userAgent = req.get('User-Agent') || '';
    if (this.isKnownMaliciousUserAgent(userAgent)) {
      suspicious.push('MALICIOUS_USER_AGENT');
    }
    
    return suspicious;
  }
  
  containsSuspiciousPattern(text) {
    return this.suspiciousPatterns.some(pattern => pattern.test(text));
  }
  
  isKnownMaliciousUserAgent(userAgent) {
    const maliciousPatterns = [
      /sqlmap/i,
      /nikto/i,
      /nessus/i,
      /burp/i
    ];
    
    return maliciousPatterns.some(pattern => pattern.test(userAgent));
  }
}

// 安全监控中间件
const securityMonitor = new SecurityMonitor();

const securityCheck = (req, res, next) => {
  const suspicious = securityMonitor.detectSuspiciousRequest(req);
  
  if (suspicious.length > 0) {
    securityLogger.warn('Suspicious Request Detected', {
      ip: req.ip,
      url: req.originalUrl,
      userAgent: req.get('User-Agent'),
      suspicious,
      timestamp: new Date().toISOString()
    });
    
    // 可以选择阻止请求或继续处理
    if (suspicious.includes('SUSPICIOUS_URL')) {
      return res.status(403).json({
        success: false,
        code: 4003,
        message: '请求被拒绝'
      });
    }
  }
  
  next();
};
```

## 🔧 安全配置管理

### 环境变量管理
```bash
# .env.example
# 数据库配置
DB_HOST=localhost
DB_USER=todo_user
DB_PASSWORD=your_secure_password
DB_NAME=school_enrollment_todo

# JWT密钥（生产环境必须使用强密钥）
JWT_ACCESS_SECRET=your_jwt_access_secret_key_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here

# 加密密钥
ENCRYPTION_KEY=your_encryption_key_here

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here

# 外部数据库配置
SMARTCAMPUS_HOST=**********
SMARTCAMPUS_USER=ethanchen
SMARTCAMPUS_PASSWORD=Spal#2@25
SMARTCAMPUS_DB=smartcampus
```

### 密钥轮换策略
```javascript
// 密钥轮换服务
class KeyRotationService {
  constructor() {
    this.currentKeyVersion = process.env.KEY_VERSION || '1';
    this.keys = {
      '1': process.env.JWT_SECRET_V1,
      '2': process.env.JWT_SECRET_V2
    };
  }
  
  // 获取当前密钥
  getCurrentKey() {
    return this.keys[this.currentKeyVersion];
  }
  
  // 验证token时支持多版本密钥
  verifyToken(token) {
    for (const [version, key] of Object.entries(this.keys)) {
      try {
        const decoded = jwt.verify(token, key);
        return { decoded, version };
      } catch (error) {
        continue;
      }
    }
    throw new Error('Invalid token');
  }
}
```

## 📋 安全检查清单

### 部署前安全检查
- [ ] 所有敏感信息已从代码中移除
- [ ] 环境变量配置正确
- [ ] HTTPS证书配置有效
- [ ] 数据库连接使用SSL
- [ ] 所有输入验证已实施
- [ ] 输出编码已实施
- [ ] 速率限制已配置
- [ ] 安全头已设置
- [ ] 日志记录已配置
- [ ] 错误处理不泄露敏感信息
- [ ] 文件上传安全检查已实施
- [ ] 权限控制已正确实施

### 定期安全维护
- [ ] 定期更新依赖包
- [ ] 定期轮换密钥
- [ ] 定期审查访问日志
- [ ] 定期进行安全扫描
- [ ] 定期备份数据
- [ ] 定期测试恢复流程
