<template>
  <div class="todo-detail-page">
    <!-- 头部 -->
    <van-nav-bar
      :title="todo?.title || '待办详情'"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    >
      <template #right>
        <van-icon name="edit" @click="editTodo" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center" />

    <!-- 详情内容 -->
    <div v-else-if="todo" class="detail-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <div class="todo-header">
          <div class="todo-status">
            <van-checkbox
              :model-value="todo.completed"
              @change="toggleStatus"
            />
            <h1 class="todo-title" :class="{ completed: todo.completed }">
              {{ todo.title }}
            </h1>
          </div>
          
          <div class="todo-priority" :class="todo.priority">
            {{ getPriorityText(todo.priority) }}
          </div>
        </div>

        <div v-if="todo.description" class="todo-description">
          <h3>描述</h3>
          <p>{{ todo.description }}</p>
        </div>

        <!-- 元信息 -->
        <div class="meta-info">
          <div class="meta-item">
            <van-icon name="bookmark-o" />
            <span class="meta-label">分类</span>
            <span class="meta-value">{{ getCategoryText(todo.category) }}</span>
          </div>
          
          <div v-if="todo.dueDate" class="meta-item">
            <van-icon name="clock-o" />
            <span class="meta-label">截止时间</span>
            <span class="meta-value" :class="getDueDateClass(todo)">
              {{ formatDueDate(todo.dueDate) }}
            </span>
          </div>
          
          <div class="meta-item">
            <van-icon name="calendar-o" />
            <span class="meta-label">创建时间</span>
            <span class="meta-value">{{ formatDate(todo.createdAt) }}</span>
          </div>
          
          <div v-if="todo.completedAt" class="meta-item">
            <van-icon name="success" />
            <span class="meta-label">完成时间</span>
            <span class="meta-value">{{ formatDate(todo.completedAt) }}</span>
          </div>
        </div>

        <!-- 进度 -->
        <div v-if="todo.progress !== undefined" class="progress-section">
          <h3>进度</h3>
          <div class="progress-content">
            <van-progress
              :percentage="todo.progress"
              stroke-width="8"
              :color="getProgressColor(todo.progress)"
            />
            <span class="progress-text">{{ todo.progress }}%</span>
          </div>
        </div>
      </div>

      <!-- 附件 -->
      <div v-if="todo.attachments?.length" class="attachments-section">
        <h3>附件 ({{ todo.attachments.length }})</h3>
        <div class="attachment-list">
          <div
            v-for="attachment in todo.attachments"
            :key="attachment.id"
            class="attachment-item"
            @click="previewAttachment(attachment)"
          >
            <div class="attachment-icon">
              <van-icon :name="getAttachmentIcon(attachment.type)" />
            </div>
            <div class="attachment-info">
              <div class="attachment-name">{{ attachment.name }}</div>
              <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
            </div>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>

      <!-- 评论 -->
      <div class="comments-section">
        <div class="comments-header">
          <h3>评论 ({{ comments.length }})</h3>
          <van-button size="small" type="primary" @click="showAddComment = true">
            添加评论
          </van-button>
        </div>
        
        <div v-if="comments.length > 0" class="comment-list">
          <div
            v-for="comment in comments"
            :key="comment.id"
            class="comment-item"
          >
            <div class="comment-avatar">
              <van-image
                :src="comment.user.avatar"
                round
                width="32"
                height="32"
                fit="cover"
              >
                <template #error>
                  <van-icon name="user-o" size="16" />
                </template>
              </van-image>
            </div>
            
            <div class="comment-content">
              <div class="comment-header">
                <span class="comment-author">{{ comment.user.name }}</span>
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
              </div>
              <div class="comment-text">{{ comment.content }}</div>
            </div>
          </div>
        </div>
        
        <van-empty v-else description="暂无评论" />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          v-if="!todo.completed"
          type="success"
          size="large"
          block
          @click="completeTodo"
        >
          标记为完成
        </van-button>
        
        <van-button
          v-else
          type="warning"
          size="large"
          block
          @click="restoreTodo"
        >
          恢复待办
        </van-button>
        
        <div class="secondary-actions">
          <van-button type="primary" plain @click="editTodo">
            编辑
          </van-button>
          <van-button type="danger" plain @click="deleteTodo">
            删除
          </van-button>
          <van-button plain @click="shareTodo">
            分享
          </van-button>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <van-empty description="待办事项不存在" />
      <van-button type="primary" @click="$router.back()">
        返回
      </van-button>
    </div>

    <!-- 添加评论弹窗 -->
    <van-popup v-model:show="showAddComment" position="bottom" round>
      <div class="add-comment-popup">
        <div class="popup-header">
          <h3>添加评论</h3>
          <van-button
            type="primary"
            size="small"
            :loading="submittingComment"
            @click="submitComment"
          >
            发布
          </van-button>
        </div>
        
        <van-field
          v-model="newComment"
          type="textarea"
          placeholder="请输入评论内容"
          rows="4"
          autosize
          maxlength="500"
          show-word-limit
        />
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useTodoStore } from '../../stores/todo'
import { useAppStore } from '../../stores/app'
import { utils } from '../../utils/global'

const router = useRouter()
const route = useRoute()
const todoStore = useTodoStore()
const appStore = useAppStore()

// 响应式数据
const loading = ref(true)
const todo = ref(null)
const comments = ref([])
const showAddComment = ref(false)
const newComment = ref('')
const submittingComment = ref(false)

// 计算属性
const todoId = computed(() => route.params.id)

// 方法
const loadTodoDetail = async () => {
  try {
    loading.value = true
    todo.value = await todoStore.getTodoById(todoId.value)
    
    // 加载评论
    await loadComments()
    
  } catch (error) {
    console.error('加载待办详情失败:', error)
    appStore.showToast('加载失败', 'error')
  } finally {
    loading.value = false
  }
}

const loadComments = async () => {
  try {
    // 这里应该调用API获取评论
    // const response = await todoAPI.getTodoComments(todoId.value)
    // comments.value = response.data
    
    // 模拟数据
    comments.value = []
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

const toggleStatus = async () => {
  try {
    await todoStore.toggleTodoStatus(todoId.value)
    todo.value.completed = !todo.value.completed
    
    if (todo.value.completed) {
      todo.value.completedAt = new Date().toISOString()
    } else {
      todo.value.completedAt = null
    }
    
    appStore.showToast(
      todo.value.completed ? '待办已完成' : '待办已恢复',
      'success'
    )
  } catch (error) {
    console.error('切换状态失败:', error)
    appStore.showToast('操作失败', 'error')
  }
}

const completeTodo = () => {
  toggleStatus()
}

const restoreTodo = () => {
  toggleStatus()
}

const editTodo = () => {
  router.push(`/todo/${todoId.value}/edit`)
}

const deleteTodo = async () => {
  try {
    await appStore.showDialog({
      title: '确认删除',
      message: '确定要删除这个待办事项吗？'
    })
    
    await todoStore.deleteTodo(todoId.value)
    appStore.showToast('删除成功', 'success')
    router.back()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      appStore.showToast('删除失败', 'error')
    }
  }
}

const shareTodo = () => {
  appStore.showToast('分享功能开发中', 'info')
}

const submitComment = async () => {
  if (!newComment.value.trim()) {
    appStore.showToast('请输入评论内容', 'warning')
    return
  }
  
  try {
    submittingComment.value = true
    
    // 这里应该调用API提交评论
    // await todoAPI.addTodoComment(todoId.value, { content: newComment.value })
    
    // 模拟添加评论
    const comment = {
      id: Date.now(),
      content: newComment.value,
      user: {
        name: '当前用户',
        avatar: ''
      },
      createdAt: new Date().toISOString()
    }
    
    comments.value.unshift(comment)
    newComment.value = ''
    showAddComment.value = false
    
    appStore.showToast('评论发布成功', 'success')
    
  } catch (error) {
    console.error('发布评论失败:', error)
    appStore.showToast('发布失败', 'error')
  } finally {
    submittingComment.value = false
  }
}

const previewAttachment = (attachment) => {
  appStore.showToast('附件预览功能开发中', 'info')
}

// 工具方法
const getPriorityText = (priority) => {
  const map = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return map[priority] || '中'
}

const getCategoryText = (category) => {
  const map = {
    academic: '学术相关',
    administrative: '行政手续',
    accommodation: '住宿相关',
    financial: '财务相关',
    health: '健康体检',
    social: '社交活动',
    other: '其他'
  }
  return map[category] || '其他'
}

const formatDate = (date) => {
  return utils.formatDate(date, 'YYYY-MM-DD HH:mm')
}

const formatDueDate = (date) => {
  return utils.formatDate(date, 'YYYY-MM-DD HH:mm')
}

const formatTime = (date) => {
  return utils.fromNow(date)
}

const formatFileSize = (size) => {
  return utils.formatFileSize(size)
}

const getDueDateClass = (todo) => {
  if (!todo.dueDate || todo.completed) return ''
  
  const now = new Date()
  const due = new Date(todo.dueDate)
  
  if (due < now) return 'overdue'
  if (due - now < 24 * 60 * 60 * 1000) return 'urgent'
  
  return ''
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#ee0a24'
  if (progress < 70) return '#ff976a'
  return '#07c160'
}

const getAttachmentIcon = (type) => {
  const iconMap = {
    image: 'photo-o',
    document: 'description',
    video: 'video-o',
    audio: 'music-o',
    archive: 'zip'
  }
  return iconMap[type] || 'description'
}

// 生命周期
onMounted(() => {
  loadTodoDetail()
})
</script>

<style lang="scss" scoped>
.todo-detail-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.detail-content {
  padding: var(--spacing-md);
  
  .basic-info {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
    
    .todo-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-lg);
      
      .todo-status {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-md);
        flex: 1;
        
        .todo-title {
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-bold);
          color: var(--text-primary);
          margin: 0;
          line-height: 1.4;
          
          &.completed {
            text-decoration: line-through;
            color: var(--text-tertiary);
          }
        }
      }
      
      .todo-priority {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        
        &.low {
          background: rgba(7, 193, 96, 0.1);
          color: var(--success-color);
        }
        
        &.medium {
          background: rgba(255, 151, 106, 0.1);
          color: var(--warning-color);
        }
        
        &.high,
        &.urgent {
          background: rgba(238, 10, 36, 0.1);
          color: var(--danger-color);
        }
      }
    }
    
    .todo-description {
      margin-bottom: var(--spacing-lg);
      
      h3 {
        font-size: var(--font-size-md);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
      }
      
      p {
        font-size: var(--font-size-md);
        color: var(--text-secondary);
        line-height: 1.6;
        margin: 0;
      }
    }
    
    .meta-info {
      .meta-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        
        .van-icon {
          color: var(--text-tertiary);
        }
        
        .meta-label {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          min-width: 60px;
        }
        
        .meta-value {
          font-size: var(--font-size-sm);
          color: var(--text-primary);
          
          &.urgent {
            color: var(--warning-color);
          }
          
          &.overdue {
            color: var(--danger-color);
          }
        }
      }
    }
    
    .progress-section {
      margin-top: var(--spacing-lg);
      
      h3 {
        font-size: var(--font-size-md);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
      }
      
      .progress-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        
        .van-progress {
          flex: 1;
        }
        
        .progress-text {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }
  }
  
  .attachments-section,
  .comments-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
    
    h3 {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      margin: 0 0 var(--spacing-md) 0;
    }
  }
  
  .attachment-list {
    .attachment-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-radius: var(--border-radius-md);
      background: var(--bg-secondary);
      margin-bottom: var(--spacing-sm);
      cursor: pointer;
      
      &:hover {
        background: var(--bg-tertiary);
      }
      
      .attachment-icon {
        .van-icon {
          font-size: 20px;
          color: var(--primary-color);
        }
      }
      
      .attachment-info {
        flex: 1;
        
        .attachment-name {
          font-size: var(--font-size-sm);
          color: var(--text-primary);
          margin-bottom: 2px;
        }
        
        .attachment-size {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
        }
      }
    }
  }
  
  .comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
  }
  
  .comment-list {
    .comment-item {
      display: flex;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      
      .comment-content {
        flex: 1;
        
        .comment-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-xs);
          
          .comment-author {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
          }
          
          .comment-time {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
          }
        }
        
        .comment-text {
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          line-height: 1.5;
        }
      }
    }
  }
  
  .action-buttons {
    margin-top: var(--spacing-lg);
    
    .secondary-actions {
      display: flex;
      gap: var(--spacing-md);
      margin-top: var(--spacing-md);
      
      .van-button {
        flex: 1;
      }
    }
  }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  gap: var(--spacing-lg);
}

.add-comment-popup {
  padding: var(--spacing-lg);
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
    }
  }
}
</style>
