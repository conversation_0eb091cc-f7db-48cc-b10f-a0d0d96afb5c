<template>
  <div class="not-found-page">
    <div class="not-found-content">
      <!-- 404 图标 -->
      <div class="error-icon">
        <div class="icon-404">404</div>
        <div class="icon-emoji">😵</div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">页面不存在</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被删除
        </p>
        <p class="error-suggestion">
          请检查网址是否正确，或返回首页重新开始
        </p>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <van-button
          type="primary"
          size="large"
          round
          @click="goHome"
        >
          返回首页
        </van-button>
        
        <van-button
          plain
          size="large"
          round
          @click="goBack"
        >
          返回上页
        </van-button>
      </div>
      
      <!-- 快捷链接 -->
      <div class="quick-links">
        <h3>您可能想要：</h3>
        <div class="link-grid">
          <div class="link-item" @click="goToTodos">
            <van-icon name="todo-list-o" />
            <span>查看待办</span>
          </div>
          <div class="link-item" @click="goToProfile">
            <van-icon name="user-o" />
            <span>个人中心</span>
          </div>
          <div class="link-item" @click="goToSettings">
            <van-icon name="setting-o" />
            <span>设置</span>
          </div>
          <div class="link-item" @click="contactSupport">
            <van-icon name="service" />
            <span>联系客服</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 装饰元素 -->
    <div class="decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useAppStore } from '../stores/app'

const router = useRouter()
const appStore = useAppStore()

// 方法
const goHome = () => {
  router.replace('/home')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    goHome()
  }
}

const goToTodos = () => {
  router.push('/todo')
}

const goToProfile = () => {
  router.push('/profile')
}

const goToSettings = () => {
  router.push('/settings')
}

const contactSupport = () => {
  appStore.showToast('客服功能开发中', 'info')
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.not-found-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.not-found-content {
  text-align: center;
  color: white;
  z-index: 2;
  max-width: 400px;
  width: 100%;
  
  .error-icon {
    margin-bottom: var(--spacing-xl);
    
    .icon-404 {
      font-size: 80px;
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--spacing-md);
      background: linear-gradient(45deg, #ff6b6b, #feca57);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .icon-emoji {
      font-size: 48px;
      animation: bounce 2s infinite;
    }
  }
  
  .error-info {
    margin-bottom: var(--spacing-xl);
    
    .error-title {
      font-size: var(--font-size-xxxl);
      font-weight: var(--font-weight-bold);
      margin: 0 0 var(--spacing-md) 0;
    }
    
    .error-description {
      font-size: var(--font-size-lg);
      margin: 0 0 var(--spacing-sm) 0;
      opacity: 0.9;
    }
    
    .error-suggestion {
      font-size: var(--font-size-md);
      margin: 0;
      opacity: 0.8;
    }
  }
  
  .error-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    
    .van-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      
      &.van-button--primary {
        background: rgba(255, 255, 255, 0.9);
        color: var(--primary-color);
        
        &:hover {
          background: white;
        }
      }
    }
  }
  
  .quick-links {
    h3 {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      margin: 0 0 var(--spacing-lg) 0;
      opacity: 0.9;
    }
    
    .link-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-md);
      
      .link-item {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg) var(--spacing-md);
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }
        
        .van-icon {
          font-size: 24px;
          margin-bottom: var(--spacing-sm);
          display: block;
        }
        
        span {
          font-size: var(--font-size-sm);
          display: block;
        }
      }
    }
  }
}

.decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  
  .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
    
    &.shape-1 {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.shape-2 {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }
    
    &.shape-3 {
      width: 60px;
      height: 60px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

// 动画
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式适配
@media (max-width: 375px) {
  .not-found-page {
    padding: var(--spacing-md);
  }
  
  .not-found-content {
    .error-icon .icon-404 {
      font-size: 60px;
    }
    
    .error-info .error-title {
      font-size: var(--font-size-xl);
    }
    
    .quick-links .link-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
