<template>
  <div class="forgot-password-page">
    <!-- 头部 -->
    <van-nav-bar
      title="忘记密码"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 重置密码表单 -->
    <div class="reset-form">
      <div class="form-header">
        <div class="icon">🔒</div>
        <h2>重置密码</h2>
        <p>请按照以下步骤重置您的密码</p>
      </div>

      <van-form @submit="handleSubmit">
        <!-- 步骤指示器 -->
        <van-steps :active="currentStep" class="reset-steps">
          <van-step>验证身份</van-step>
          <van-step>设置新密码</van-step>
          <van-step>完成</van-step>
        </van-steps>

        <!-- 第一步：验证身份 -->
        <div v-show="currentStep === 0" class="step-content">
          <van-tabs v-model:active="verifyType" class="verify-tabs">
            <van-tab title="手机验证" name="phone">
              <div class="verify-content">
                <van-field
                  v-model="form.phone"
                  name="phone"
                  label="手机号"
                  placeholder="请输入注册时的手机号"
                  left-icon="phone-o"
                  :rules="[
                    { required: true, message: '请输入手机号' },
                    { validator: validatePhone, message: '请输入正确的手机号' }
                  ]"
                  clearable
                />
                
                <van-field
                  v-model="form.phoneCode"
                  name="phoneCode"
                  label="验证码"
                  placeholder="请输入验证码"
                  left-icon="shield-o"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                  clearable
                >
                  <template #button>
                    <van-button
                      size="small"
                      type="primary"
                      :disabled="!canSendPhoneCode || phoneCountdown > 0"
                      :loading="sendingPhoneCode"
                      @click="sendPhoneCode"
                    >
                      {{ phoneCountdown > 0 ? `${phoneCountdown}s` : '发送验证码' }}
                    </van-button>
                  </template>
                </van-field>
              </div>
            </van-tab>
            
            <van-tab title="邮箱验证" name="email">
              <div class="verify-content">
                <van-field
                  v-model="form.email"
                  name="email"
                  label="邮箱"
                  placeholder="请输入注册时的邮箱"
                  left-icon="envelop-o"
                  :rules="[
                    { required: true, message: '请输入邮箱' },
                    { validator: validateEmail, message: '请输入正确的邮箱格式' }
                  ]"
                  clearable
                />
                
                <van-field
                  v-model="form.emailCode"
                  name="emailCode"
                  label="验证码"
                  placeholder="请输入验证码"
                  left-icon="shield-o"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                  clearable
                >
                  <template #button>
                    <van-button
                      size="small"
                      type="primary"
                      :disabled="!canSendEmailCode || emailCountdown > 0"
                      :loading="sendingEmailCode"
                      @click="sendEmailCode"
                    >
                      {{ emailCountdown > 0 ? `${emailCountdown}s` : '发送验证码' }}
                    </van-button>
                  </template>
                </van-field>
              </div>
            </van-tab>
          </van-tabs>
        </div>

        <!-- 第二步：设置新密码 -->
        <div v-show="currentStep === 1" class="step-content">
          <van-field
            v-model="form.newPassword"
            type="password"
            name="newPassword"
            label="新密码"
            placeholder="请输入新密码"
            left-icon="lock"
            :rules="[
              { required: true, message: '请输入新密码' },
              { validator: validatePassword, message: '密码强度不够' }
            ]"
            clearable
          />
          
          <van-field
            v-model="form.confirmPassword"
            type="password"
            name="confirmPassword"
            label="确认密码"
            placeholder="请再次输入新密码"
            left-icon="lock"
            :rules="[
              { required: true, message: '请确认密码' },
              { validator: validateConfirmPassword, message: '两次输入的密码不一致' }
            ]"
            clearable
          />
          
          <!-- 密码强度指示器 -->
          <div class="password-strength">
            <div class="strength-label">密码强度：</div>
            <div class="strength-bar">
              <div 
                class="strength-fill"
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthWidth }"
              ></div>
            </div>
            <div class="strength-text">{{ passwordStrengthText }}</div>
          </div>
        </div>

        <!-- 第三步：完成 -->
        <div v-show="currentStep === 2" class="step-content">
          <div class="success-content">
            <div class="success-icon">✅</div>
            <h3>密码重置成功</h3>
            <p>您的密码已成功重置，请使用新密码登录</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <van-button
            v-if="currentStep === 0"
            round
            block
            type="primary"
            size="large"
            @click="verifyIdentity"
            :loading="verifying"
          >
            验证身份
          </van-button>
          
          <van-button
            v-else-if="currentStep === 1"
            round
            block
            type="primary"
            size="large"
            @click="resetPassword"
            :loading="resetting"
          >
            重置密码
          </van-button>
          
          <van-button
            v-else
            round
            block
            type="primary"
            size="large"
            @click="goToLogin"
          >
            去登录
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '../../stores/app'
import { validatePhone, validateEmail, validatePassword } from '../../utils/validate'
import { authAPI } from '../../api/auth'

const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const currentStep = ref(0)
const verifyType = ref('phone')
const verifying = ref(false)
const resetting = ref(false)
const sendingPhoneCode = ref(false)
const sendingEmailCode = ref(false)
const phoneCountdown = ref(0)
const emailCountdown = ref(0)

const form = reactive({
  phone: '',
  email: '',
  phoneCode: '',
  emailCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const canSendPhoneCode = computed(() => {
  return validatePhone(form.phone)
})

const canSendEmailCode = computed(() => {
  return validateEmail(form.email)
})

const passwordStrength = computed(() => {
  return validatePassword(form.newPassword)
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value.strength
  return {
    'strength-weak': strength === 'weak',
    'strength-medium': strength === 'medium',
    'strength-strong': strength === 'strong'
  }
})

const passwordStrengthWidth = computed(() => {
  const score = passwordStrength.value.score
  return `${(score / 5) * 100}%`
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value.strength
  const textMap = {
    weak: '弱',
    medium: '中',
    strong: '强'
  }
  return textMap[strength] || '弱'
})

// 方法
const validateConfirmPassword = (value) => {
  return value === form.newPassword
}

const sendPhoneCode = async () => {
  if (!canSendPhoneCode.value || sendingPhoneCode.value) return
  
  try {
    sendingPhoneCode.value = true
    
    await authAPI.sendSmsCode({
      phone: form.phone,
      type: 'reset'
    })
    
    appStore.showToast('验证码已发送', 'success')
    
    // 开始倒计时
    phoneCountdown.value = 60
    const timer = setInterval(() => {
      phoneCountdown.value--
      if (phoneCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    appStore.showToast(error.message || '发送验证码失败', 'error')
  } finally {
    sendingPhoneCode.value = false
  }
}

const sendEmailCode = async () => {
  if (!canSendEmailCode.value || sendingEmailCode.value) return
  
  try {
    sendingEmailCode.value = true
    
    await authAPI.sendEmailCode({
      email: form.email,
      type: 'reset'
    })
    
    appStore.showToast('验证码已发送', 'success')
    
    // 开始倒计时
    emailCountdown.value = 60
    const timer = setInterval(() => {
      emailCountdown.value--
      if (emailCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    appStore.showToast(error.message || '发送验证码失败', 'error')
  } finally {
    sendingEmailCode.value = false
  }
}

const verifyIdentity = async () => {
  try {
    verifying.value = true
    
    const verifyData = {
      type: verifyType.value,
      ...(verifyType.value === 'phone' 
        ? { phone: form.phone, code: form.phoneCode }
        : { email: form.email, code: form.emailCode }
      )
    }
    
    await authAPI.verifySmsCode(verifyData)
    
    appStore.showToast('身份验证成功', 'success')
    currentStep.value = 1
    
  } catch (error) {
    console.error('身份验证失败:', error)
    appStore.showToast(error.message || '身份验证失败', 'error')
  } finally {
    verifying.value = false
  }
}

const resetPassword = async () => {
  try {
    resetting.value = true
    
    const resetData = {
      type: verifyType.value,
      newPassword: form.newPassword,
      ...(verifyType.value === 'phone' 
        ? { phone: form.phone, code: form.phoneCode }
        : { email: form.email, code: form.emailCode }
      )
    }
    
    await authAPI.resetPassword(resetData)
    
    appStore.showToast('密码重置成功', 'success')
    currentStep.value = 2
    
  } catch (error) {
    console.error('密码重置失败:', error)
    appStore.showToast(error.message || '密码重置失败', 'error')
  } finally {
    resetting.value = false
  }
}

const goToLogin = () => {
  router.replace('/login')
}

const handleSubmit = () => {
  // 表单提交处理
}
</script>

<style lang="scss" scoped>
.forgot-password-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.reset-form {
  padding: var(--spacing-lg);
  
  .form-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    
    .icon {
      font-size: 48px;
      margin-bottom: var(--spacing-md);
    }
    
    h2 {
      color: var(--text-primary);
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      margin: 0 0 var(--spacing-sm) 0;
    }
    
    p {
      color: var(--text-secondary);
      font-size: var(--font-size-md);
      margin: 0;
    }
  }
  
  .reset-steps {
    margin-bottom: var(--spacing-xl);
  }
  
  .step-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    
    .verify-tabs {
      margin-bottom: var(--spacing-md);
    }
    
    .verify-content {
      padding: var(--spacing-md) 0;
      
      .van-field {
        margin-bottom: var(--spacing-md);
      }
    }
    
    .password-strength {
      display: flex;
      align-items: center;
      margin-top: var(--spacing-md);
      font-size: var(--font-size-sm);
      
      .strength-label {
        margin-right: var(--spacing-sm);
        color: var(--text-secondary);
      }
      
      .strength-bar {
        flex: 1;
        height: 4px;
        background: var(--bg-tertiary);
        border-radius: 2px;
        margin-right: var(--spacing-sm);
        overflow: hidden;
        
        .strength-fill {
          height: 100%;
          transition: all 0.3s ease;
          
          &.strength-weak {
            background: var(--danger-color);
          }
          
          &.strength-medium {
            background: var(--warning-color);
          }
          
          &.strength-strong {
            background: var(--success-color);
          }
        }
      }
      
      .strength-text {
        color: var(--text-tertiary);
      }
    }
    
    .success-content {
      text-align: center;
      padding: var(--spacing-xl) 0;
      
      .success-icon {
        font-size: 64px;
        margin-bottom: var(--spacing-lg);
      }
      
      h3 {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        margin: 0 0 var(--spacing-md) 0;
      }
      
      p {
        color: var(--text-secondary);
        font-size: var(--font-size-md);
        margin: 0;
      }
    }
  }
  
  .form-actions {
    margin-bottom: var(--spacing-lg);
  }
}
</style>
