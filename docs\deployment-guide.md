# 部署运维指南

## 📋 概述

本文档详细描述了入学待办事项系统的部署架构、环境配置、部署流程和运维监控方案。

## 🏗️ 部署架构

### 生产环境架构图
```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │                 │
            ┌───────▼──────┐  ┌──────▼──────┐
            │  Web Server  │  │ Web Server  │
            │   (Node.js)  │  │  (Node.js)  │
            └───────┬──────┘  └──────┬──────┘
                    │                │
                    └─────────┬──────┘
                              │
                    ┌─────────▼───────┐
                    │   Database      │
                    │   (MySQL)       │
                    └─────────────────┘
                              │
                    ┌─────────▼───────┐
                    │     Redis       │
                    │   (Cache)       │
                    └─────────────────┘
```

### 服务器配置要求

#### 生产环境
```yaml
# 前端服务器 (2台)
Frontend Servers:
  CPU: 4 cores
  RAM: 8GB
  Storage: 100GB SSD
  OS: Ubuntu 22.04 LTS

# 后端服务器 (2台)
Backend Servers:
  CPU: 8 cores
  RAM: 16GB
  Storage: 200GB SSD
  OS: Ubuntu 22.04 LTS

# 数据库服务器 (1台主 + 1台从)
Database Servers:
  CPU: 8 cores
  RAM: 32GB
  Storage: 500GB SSD (RAID 1)
  OS: Ubuntu 22.04 LTS

# 缓存服务器 (1台)
Cache Server:
  CPU: 4 cores
  RAM: 16GB
  Storage: 100GB SSD
  OS: Ubuntu 22.04 LTS
```

#### 开发/测试环境
```yaml
# 单台服务器配置
Development Server:
  CPU: 4 cores
  RAM: 8GB
  Storage: 100GB SSD
  OS: Ubuntu 22.04 LTS
```

## 🔧 环境配置

### 1. 系统依赖安装

#### Node.js环境
```bash
# 安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version  # v18.x.x
npm --version   # 9.x.x

# 安装PM2进程管理器
sudo npm install -g pm2
```

#### MySQL数据库
```bash
# 安装MySQL 8.0
sudo apt update
sudo apt install mysql-server-8.0

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE school_enrollment_todo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'todo_user'@'localhost' IDENTIFIED BY 'your_secure_password';
CREATE USER 'todo_user'@'%' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON school_enrollment_todo.* TO 'todo_user'@'localhost';
GRANT ALL PRIVILEGES ON school_enrollment_todo.* TO 'todo_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

#### Redis缓存
```bash
# 安装Redis
sudo apt install redis-server

# 配置Redis
sudo nano /etc/redis/redis.conf

# 修改配置
bind 127.0.0.1 ::1
requirepass your_redis_password
maxmemory 2gb
maxmemory-policy allkeys-lru

# 重启Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

#### Nginx负载均衡
```bash
# 安装Nginx
sudo apt install nginx

# 配置文件
sudo nano /etc/nginx/sites-available/todo-system
```

```nginx
# Nginx配置
upstream backend {
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

# H5移动端
server {
    listen 80;
    listen 443 ssl http2;
    server_name student.enrollment-todo.edu.cn;

    # SSL配置
    ssl_certificate /etc/ssl/certs/todo-system.crt;
    ssl_certificate_key /etc/ssl/private/todo-system.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256;

    # 强制HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }

    # 静态文件
    location / {
        root /var/www/mobile-h5/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理
    location /api/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# 管理后台
server {
    listen 80;
    listen 443 ssl http2;
    server_name admin.enrollment-todo.edu.cn;

    # SSL配置（同上）
    ssl_certificate /etc/ssl/certs/todo-system.crt;
    ssl_certificate_key /etc/ssl/private/todo-system.key;
    ssl_protocols TLSv1.2 TLSv1.3;

    # 强制HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }

    # 静态文件
    location / {
        root /var/www/admin-web/dist;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend;
        # 其他配置同上
    }
}
```

### 2. 环境变量配置

#### 生产环境配置
```bash
# /opt/todo-system/.env.production
NODE_ENV=production
PORT=3001

# 数据库配置
DB_HOST=localhost
DB_USER=todo_user
DB_PASSWORD=your_secure_password
DB_NAME=school_enrollment_todo

# 外部数据库配置
SMARTCAMPUS_HOST=**********
SMARTCAMPUS_USER=ethanchen
SMARTCAMPUS_PASSWORD=Spal#2@25
SMARTCAMPUS_DB=smartcampus

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT密钥
JWT_ACCESS_SECRET=your_jwt_access_secret_key_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here

# 文件上传配置
UPLOAD_PATH=/opt/todo-system/uploads
UPLOAD_MAX_SIZE=10485760

# 日志配置
LOG_LEVEL=info
LOG_PATH=/opt/todo-system/logs

# 监控配置
ENABLE_MONITORING=true
HEALTH_CHECK_INTERVAL=30000
```

## 🚀 部署流程

### 1. 自动化部署脚本

#### 部署脚本 (deploy.sh)
```bash
#!/bin/bash

# 部署配置
PROJECT_NAME="todo-system"
DEPLOY_PATH="/opt/$PROJECT_NAME"
BACKUP_PATH="/backup/$PROJECT_NAME"
GIT_REPO="https://github.com/your-org/todo-system.git"
BRANCH="main"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# 检查权限
if [[ $EUID -ne 0 ]]; then
   error "请使用root权限运行此脚本"
fi

# 创建备份
backup() {
    log "创建备份..."
    if [ -d "$DEPLOY_PATH" ]; then
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_PATH"
        cp -r "$DEPLOY_PATH" "$BACKUP_PATH/$BACKUP_NAME"
        log "备份完成: $BACKUP_PATH/$BACKUP_NAME"
    fi
}

# 下载代码
download_code() {
    log "下载最新代码..."
    
    if [ -d "$DEPLOY_PATH" ]; then
        cd "$DEPLOY_PATH"
        git fetch origin
        git reset --hard origin/$BRANCH
    else
        git clone -b $BRANCH $GIT_REPO $DEPLOY_PATH
        cd "$DEPLOY_PATH"
    fi
    
    log "代码下载完成"
}

# 安装依赖
install_dependencies() {
    log "安装后端依赖..."
    cd "$DEPLOY_PATH/backend"
    npm ci --production
    
    log "构建前端..."
    cd "$DEPLOY_PATH/mobile-h5"
    npm ci
    npm run build
    
    cd "$DEPLOY_PATH/admin-web"
    npm ci
    npm run build
    
    log "依赖安装完成"
}

# 数据库迁移
migrate_database() {
    log "执行数据库迁移..."
    cd "$DEPLOY_PATH/backend"
    npm run migrate
    log "数据库迁移完成"
}

# 部署前端
deploy_frontend() {
    log "部署前端文件..."
    
    # 部署H5移动端
    rm -rf /var/www/mobile-h5/dist
    cp -r "$DEPLOY_PATH/mobile-h5/dist" /var/www/mobile-h5/
    
    # 部署管理后台
    rm -rf /var/www/admin-web/dist
    cp -r "$DEPLOY_PATH/admin-web/dist" /var/www/admin-web/
    
    # 设置权限
    chown -R www-data:www-data /var/www/mobile-h5
    chown -R www-data:www-data /var/www/admin-web
    
    log "前端部署完成"
}

# 重启服务
restart_services() {
    log "重启后端服务..."
    
    # 停止旧进程
    pm2 stop $PROJECT_NAME || true
    pm2 delete $PROJECT_NAME || true
    
    # 启动新进程
    cd "$DEPLOY_PATH/backend"
    pm2 start ecosystem.config.js --env production
    
    # 重启Nginx
    systemctl reload nginx
    
    log "服务重启完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    sleep 10  # 等待服务启动
    
    # 检查后端API
    if curl -f -s http://localhost:3001/api/v1/health > /dev/null; then
        log "后端服务健康检查通过"
    else
        error "后端服务健康检查失败"
    fi
    
    # 检查前端
    if curl -f -s http://localhost/api/v1/health > /dev/null; then
        log "前端服务健康检查通过"
    else
        error "前端服务健康检查失败"
    fi
    
    log "所有服务健康检查通过"
}

# 主部署流程
main() {
    log "开始部署 $PROJECT_NAME..."
    
    backup
    download_code
    install_dependencies
    migrate_database
    deploy_frontend
    restart_services
    health_check
    
    log "部署完成！"
}

# 执行部署
main "$@"
```

#### PM2配置文件 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [
    {
      name: 'todo-system',
      script: './src/server.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      
      // 日志配置
      log_file: '/opt/todo-system/logs/combined.log',
      out_file: '/opt/todo-system/logs/out.log',
      error_file: '/opt/todo-system/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 重启配置
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      
      // 监控配置
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      
      // 其他配置
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000
    }
  ]
};
```

### 2. CI/CD流程

#### GitHub Actions配置 (.github/workflows/deploy.yml)
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd backend
        npm ci
    
    - name: Run tests
      run: |
        cd backend
        npm test
      env:
        NODE_ENV: test
        DB_HOST: 127.0.0.1
        DB_USER: root
        DB_PASSWORD: root
        DB_NAME: test_db

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /opt/todo-system
          ./deploy.sh
```

## 📊 监控配置

### 1. 系统监控

#### Prometheus配置 (prometheus.yml)
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'todo-system'
    static_configs:
      - targets: ['localhost:3001', 'localhost:3002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['localhost:9104']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - localhost:9093
```

#### 告警规则 (alert_rules.yml)
```yaml
groups:
  - name: todo-system-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "高错误率告警"
          description: "错误率超过10%，持续5分钟"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "95%的请求响应时间超过1秒"

      - alert: DatabaseConnectionFailed
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接失败"
          description: "MySQL数据库连接失败"
```

### 2. 应用监控

#### 健康检查端点
```javascript
// routes/health.js
const express = require('express');
const router = express.Router();

router.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    
    // 数据库连接检查
    database: {
      todoSystem: 'unknown',
      smartcampus: 'unknown'
    }
  };

  try {
    // 检查主数据库
    await db.todoSystem.execute('SELECT 1');
    health.database.todoSystem = 'connected';
  } catch (error) {
    health.database.todoSystem = 'disconnected';
    health.status = 'unhealthy';
  }

  try {
    // 检查外部数据库
    await db.smartcampus.execute('SELECT 1');
    health.database.smartcampus = 'connected';
  } catch (error) {
    health.database.smartcampus = 'disconnected';
    health.status = 'degraded';
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});

module.exports = router;
```

### 3. 日志管理

#### 日志配置
```javascript
// config/logger.js
const winston = require('winston');
const path = require('path');

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'todo-system' },
  transports: [
    // 错误日志
    new winston.transports.File({
      filename: path.join(process.env.LOG_PATH || './logs', 'error.log'),
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 10
    }),
    
    // 综合日志
    new winston.transports.File({
      filename: path.join(process.env.LOG_PATH || './logs', 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10
    }),
    
    // 控制台输出（开发环境）
    ...(process.env.NODE_ENV !== 'production' ? [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      })
    ] : [])
  ]
});

module.exports = logger;
```

#### 日志轮转配置 (logrotate)
```bash
# /etc/logrotate.d/todo-system
/opt/todo-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔄 备份恢复

### 1. 数据库备份脚本
```bash
#!/bin/bash
# backup-db.sh

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="school_enrollment_todo"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h localhost -u backup_user -p$BACKUP_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  $DB_NAME > $BACKUP_DIR/${DB_NAME}_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/${DB_NAME}_$DATE.sql

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "数据库备份完成: ${DB_NAME}_$DATE.sql.gz"
```

### 2. 文件备份脚本
```bash
#!/bin/bash
# backup-files.sh

BACKUP_DIR="/backup/files"
DATE=$(date +%Y%m%d_%H%M%S)
SOURCE_DIR="/opt/todo-system/uploads"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $SOURCE_DIR .

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "文件备份完成: uploads_$DATE.tar.gz"
```

### 3. 定时备份 (crontab)
```bash
# 编辑定时任务
crontab -e

# 添加备份任务
# 每天凌晨2点备份数据库
0 2 * * * /opt/todo-system/scripts/backup-db.sh

# 每天凌晨3点备份文件
0 3 * * * /opt/todo-system/scripts/backup-files.sh

# 每周日凌晨4点清理日志
0 4 * * 0 find /opt/todo-system/logs -name "*.log" -mtime +7 -delete
```

## 🚨 故障处理

### 常见问题排查

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :3001

# 检查PM2状态
pm2 status

# 查看错误日志
pm2 logs todo-system --lines 100

# 检查配置文件
node -c /opt/todo-system/backend/src/server.js
```

#### 2. 数据库连接问题
```bash
# 测试数据库连接
mysql -h localhost -u todo_user -p school_enrollment_todo

# 检查数据库状态
systemctl status mysql

# 查看数据库错误日志
tail -f /var/log/mysql/error.log
```

#### 3. 性能问题排查
```bash
# 检查系统资源
top
htop
iotop

# 检查数据库性能
mysql -e "SHOW PROCESSLIST;"
mysql -e "SHOW ENGINE INNODB STATUS\G"

# 检查应用性能
pm2 monit
```

### 应急处理流程

#### 1. 服务降级
```javascript
// 服务降级中间件
const circuitBreaker = require('opossum');

const options = {
  timeout: 3000,
  errorThresholdPercentage: 50,
  resetTimeout: 30000
};

const breaker = circuitBreaker(externalServiceCall, options);

breaker.fallback(() => {
  return { 
    success: false, 
    message: '服务暂时不可用，请稍后重试' 
  };
});
```

#### 2. 快速回滚
```bash
#!/bin/bash
# rollback.sh

BACKUP_PATH="/backup/todo-system"
DEPLOY_PATH="/opt/todo-system"

# 获取最新备份
LATEST_BACKUP=$(ls -t $BACKUP_PATH | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "没有找到备份文件"
    exit 1
fi

echo "回滚到备份: $LATEST_BACKUP"

# 停止服务
pm2 stop todo-system

# 恢复代码
rm -rf $DEPLOY_PATH
cp -r $BACKUP_PATH/$LATEST_BACKUP $DEPLOY_PATH

# 重启服务
cd $DEPLOY_PATH/backend
pm2 start ecosystem.config.js --env production

echo "回滚完成"
```
