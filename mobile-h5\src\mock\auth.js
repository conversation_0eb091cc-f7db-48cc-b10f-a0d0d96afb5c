// 模拟学生数据库
const mockStudents = [
  {
    stuid: '2024001001',
    name: '张三',
    class: '计算机科学与技术1班',
    major: '计算机科学与技术',
    grade: '2024',
    status: 'active'
  },
  {
    stuid: '2024001002', 
    name: '李四',
    class: '软件工程1班',
    major: '软件工程',
    grade: '2024',
    status: 'active'
  },
  {
    stuid: '2024001003',
    name: '王五',
    class: '信息安全1班', 
    major: '信息安全',
    grade: '2024',
    status: 'active'
  },
  {
    stuid: '2023001001',
    name: '赵六',
    class: '计算机科学与技术2班',
    major: '计算机科学与技术', 
    grade: '2023',
    status: 'active'
  },
  {
    stuid: '2023001002',
    name: '钱七',
    class: '软件工程2班',
    major: '软件工程',
    grade: '2023', 
    status: 'active'
  }
]

// 模拟管理员数据
const mockAdmins = [
  {
    username: 'admin',
    password: 'admin123',
    name: '系统管理员',
    role: 'admin'
  },
  {
    username: 'teacher',
    password: 'teacher123', 
    name: '教务老师',
    role: 'teacher'
  }
]

// 学号登录API
export const studentLogin = (studentId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const student = mockStudents.find(s => s.stuid === studentId)
      
      if (student) {
        const token = `student_token_${studentId}_${Date.now()}`
        resolve({
          code: 200,
          message: '登录成功',
          data: {
            token,
            user: {
              id: student.stuid,
              studentId: student.stuid,
              name: student.name,
              class: student.class,
              major: student.major,
              grade: student.grade,
              role: 'student',
              avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${student.name}`,
              loginTime: new Date().toISOString()
            }
          }
        })
      } else {
        reject({
          code: 404,
          message: '学号不存在，请检查后重试'
        })
      }
    }, 1000) // 模拟网络延迟
  })
}

// 管理员登录API
export const adminLogin = (username, password) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const admin = mockAdmins.find(a => a.username === username && a.password === password)
      
      if (admin) {
        const token = `admin_token_${username}_${Date.now()}`
        resolve({
          code: 200,
          message: '登录成功',
          data: {
            token,
            user: {
              id: admin.username,
              username: admin.username,
              name: admin.name,
              role: admin.role,
              avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${admin.name}`,
              loginTime: new Date().toISOString()
            }
          }
        })
      } else {
        reject({
          code: 401,
          message: '用户名或密码错误'
        })
      }
    }, 1000)
  })
}

// 验证学号是否存在
export const validateStudentId = (studentId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const student = mockStudents.find(s => s.stuid === studentId)
      
      if (student) {
        resolve({
          code: 200,
          message: '学号有效',
          data: {
            valid: true,
            studentInfo: {
              name: student.name,
              class: student.class,
              major: student.major
            }
          }
        })
      } else {
        reject({
          code: 404,
          message: '学号不存在'
        })
      }
    }, 500)
  })
}

// 获取用户信息
export const getUserInfo = (token) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (token.startsWith('student_token_')) {
        const studentId = token.split('_')[2]
        const student = mockStudents.find(s => s.stuid === studentId)
        
        if (student) {
          resolve({
            code: 200,
            data: {
              id: student.stuid,
              studentId: student.stuid,
              name: student.name,
              class: student.class,
              major: student.major,
              grade: student.grade,
              role: 'student',
              avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${student.name}`
            }
          })
        } else {
          reject({ code: 401, message: 'Token无效' })
        }
      } else if (token.startsWith('admin_token_')) {
        const username = token.split('_')[2]
        const admin = mockAdmins.find(a => a.username === username)
        
        if (admin) {
          resolve({
            code: 200,
            data: {
              id: admin.username,
              username: admin.username,
              name: admin.name,
              role: admin.role,
              avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${admin.name}`
            }
          })
        } else {
          reject({ code: 401, message: 'Token无效' })
        }
      } else {
        reject({ code: 401, message: 'Token格式错误' })
      }
    }, 500)
  })
}

export default {
  studentLogin,
  adminLogin,
  validateStudentId,
  getUserInfo
}
