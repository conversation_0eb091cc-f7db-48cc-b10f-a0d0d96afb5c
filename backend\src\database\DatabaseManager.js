const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

/**
 * 数据库管理器 - 管理双数据库连接
 * 1. todoSystemDB - 本地待办系统数据库 (读写)
 * 2. smartcampusDB - 外部学生信息数据库 (只读)
 */
class DatabaseManager {
  constructor() {
    this.todoSystemDB = null;
    this.smartcampusDB = null;
    this.isConnected = false;
  }

  /**
   * 建立数据库连接
   */
  async connect() {
    try {
      // 连接待办系统数据库
      await this.connectTodoSystemDB();
      
      // 连接学生信息数据库
      await this.connectSmartcampusDB();
      
      this.isConnected = true;
      logger.info('All database connections established successfully');
    } catch (error) {
      logger.error('Failed to establish database connections', { error: error.message });
      throw error;
    }
  }

  /**
   * 连接待办系统数据库
   */
  async connectTodoSystemDB() {
    const config = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4',
      timezone: '+08:00',
      acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
      timeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 60000,
      reconnect: true,
      pool: {
        min: 5,
        max: 20,
        acquire: 30000,
        idle: 10000,
        evict: 1000,
        handleDisconnects: true
      },
      // SSL配置 (生产环境)
      ...(process.env.NODE_ENV === 'production' && {
        ssl: {
          rejectUnauthorized: false
        }
      })
    };

    try {
      this.todoSystemDB = mysql.createPool(config);
      
      // 测试连接
      const connection = await this.todoSystemDB.getConnection();
      await connection.ping();
      connection.release();
      
      logger.info('Todo system database connected successfully', {
        host: config.host,
        database: config.database
      });
    } catch (error) {
      logger.error('Failed to connect to todo system database', {
        error: error.message,
        host: config.host,
        database: config.database
      });
      throw error;
    }
  }

  /**
   * 连接学生信息数据库 (smartcampus)
   */
  async connectSmartcampusDB() {
    const config = {
      host: process.env.SMARTCAMPUS_HOST,
      port: parseInt(process.env.SMARTCAMPUS_PORT) || 3306,
      user: process.env.SMARTCAMPUS_USER,
      password: process.env.SMARTCAMPUS_PASSWORD,
      database: process.env.SMARTCAMPUS_DB,
      charset: 'utf8mb4',
      timezone: '+08:00',
      acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
      timeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 60000,
      reconnect: true,
      pool: {
        min: 2,
        max: 10,
        acquire: 30000,
        idle: 10000,
        evict: 1000,
        handleDisconnects: true
      }
    };

    try {
      this.smartcampusDB = mysql.createPool(config);
      
      // 测试连接
      const connection = await this.smartcampusDB.getConnection();
      await connection.ping();
      connection.release();
      
      logger.info('Smartcampus database connected successfully', {
        host: config.host,
        database: config.database
      });
    } catch (error) {
      logger.error('Failed to connect to smartcampus database', {
        error: error.message,
        host: config.host,
        database: config.database
      });
      
      // 在开发环境中，smartcampus连接失败不应该阻止应用启动
      if (process.env.NODE_ENV === 'development') {
        logger.warn('Smartcampus database connection failed in development mode, continuing...');
        this.smartcampusDB = null;
      } else {
        throw error;
      }
    }
  }

  /**
   * 查询学生信息 (从smartcampus数据库)
   * @param {string} studentId - 学号
   * @returns {Object|null} 学生信息
   */
  async getStudentInfo(studentId) {
    if (!this.smartcampusDB) {
      logger.warn('Smartcampus database not available, using fallback');
      return this.getStudentInfoFallback(studentId);
    }

    try {
      const [rows] = await this.smartcampusDB.execute(
        'SELECT stuid, stucName FROM stuinfosp WHERE stuid = ? LIMIT 1',
        [studentId]
      );
      
      if (rows.length > 0) {
        logger.info('Student info retrieved from smartcampus', { studentId });
        return {
          stuid: rows[0].stuid,
          stucName: rows[0].stucName
        };
      }
      
      logger.info('Student not found in smartcampus', { studentId });
      return null;
    } catch (error) {
      logger.error('Failed to query student info from smartcampus', {
        error: error.message,
        studentId
      });
      
      // 降级到本地缓存
      return this.getStudentInfoFallback(studentId);
    }
  }

  /**
   * 降级方案：从本地缓存获取学生信息
   * @param {string} studentId - 学号
   * @returns {Object|null} 学生信息
   */
  async getStudentInfoFallback(studentId) {
    try {
      const [rows] = await this.todoSystemDB.execute(
        'SELECT student_id as stuid, real_name as stucName FROM users WHERE student_id = ? LIMIT 1',
        [studentId]
      );
      
      if (rows.length > 0) {
        logger.info('Student info retrieved from local cache', { studentId });
        return {
          stuid: rows[0].stuid,
          stucName: rows[0].stucName
        };
      }
      
      return null;
    } catch (error) {
      logger.error('Failed to query student info from local cache', {
        error: error.message,
        studentId
      });
      return null;
    }
  }

  /**
   * 创建或更新本地用户记录
   * @param {Object} studentInfo - 学生信息
   * @returns {Object} 本地用户信息
   */
  async createOrUpdateLocalUser(studentInfo) {
    const { stuid, stucName } = studentInfo;
    
    try {
      // 检查用户是否已存在
      const [existingUsers] = await this.todoSystemDB.execute(
        'SELECT id, student_id, real_name, created_at FROM users WHERE student_id = ?',
        [stuid]
      );

      if (existingUsers.length === 0) {
        // 创建新用户
        const [result] = await this.todoSystemDB.execute(
          `INSERT INTO users (student_id, real_name, nickname, role, created_at) 
           VALUES (?, ?, ?, 'student', NOW())`,
          [stuid, stucName, stucName]
        );
        
        logger.info('New user created', { studentId: stuid, userId: result.insertId });
        
        return {
          id: result.insertId,
          studentId: stuid,
          realName: stucName,
          nickname: stucName,
          role: 'student'
        };
      } else {
        // 更新现有用户信息
        await this.todoSystemDB.execute(
          'UPDATE users SET real_name = ?, nickname = ?, last_sync_time = NOW() WHERE student_id = ?',
          [stucName, stucName, stuid]
        );
        
        logger.info('User info updated', { studentId: stuid, userId: existingUsers[0].id });
        
        return {
          id: existingUsers[0].id,
          studentId: stuid,
          realName: stucName,
          nickname: stucName,
          role: 'student'
        };
      }
    } catch (error) {
      logger.error('Failed to create or update local user', {
        error: error.message,
        studentId: stuid
      });
      throw error;
    }
  }

  /**
   * 根据学号获取本地用户信息
   * @param {string} studentId - 学号
   * @returns {Object|null} 用户信息
   */
  async getLocalUserByStudentId(studentId) {
    try {
      const [rows] = await this.todoSystemDB.execute(
        `SELECT id, student_id, real_name, nickname, role, language, theme, 
                is_active, last_login_time, created_at 
         FROM users WHERE student_id = ? AND is_active = 1 LIMIT 1`,
        [studentId]
      );
      
      if (rows.length > 0) {
        const user = rows[0];
        return {
          id: user.id,
          studentId: user.student_id,
          realName: user.real_name,
          nickname: user.nickname,
          role: user.role,
          language: user.language || 'zh-cn',
          theme: user.theme || 'light',
          isActive: user.is_active,
          lastLoginTime: user.last_login_time,
          createdAt: user.created_at
        };
      }
      
      return null;
    } catch (error) {
      logger.error('Failed to get local user by student ID', {
        error: error.message,
        studentId
      });
      throw error;
    }
  }

  /**
   * 更新用户最后登录时间
   * @param {number} userId - 用户ID
   */
  async updateLastLoginTime(userId) {
    try {
      await this.todoSystemDB.execute(
        'UPDATE users SET last_login_time = NOW() WHERE id = ?',
        [userId]
      );
    } catch (error) {
      logger.error('Failed to update last login time', {
        error: error.message,
        userId
      });
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 健康检查
   * @returns {Object} 健康状态
   */
  async healthCheck() {
    const health = {
      todoSystem: 'unknown',
      smartcampus: 'unknown'
    };

    // 检查待办系统数据库
    try {
      await this.todoSystemDB.execute('SELECT 1');
      health.todoSystem = 'connected';
    } catch (error) {
      health.todoSystem = 'disconnected';
      logger.error('Todo system database health check failed', { error: error.message });
    }

    // 检查学生信息数据库
    if (this.smartcampusDB) {
      try {
        await this.smartcampusDB.execute('SELECT 1');
        health.smartcampus = 'connected';
      } catch (error) {
        health.smartcampus = 'disconnected';
        logger.error('Smartcampus database health check failed', { error: error.message });
      }
    } else {
      health.smartcampus = 'not_configured';
    }

    return health;
  }

  /**
   * 关闭数据库连接
   */
  async close() {
    try {
      if (this.todoSystemDB) {
        await this.todoSystemDB.end();
        logger.info('Todo system database connection closed');
      }
      
      if (this.smartcampusDB) {
        await this.smartcampusDB.end();
        logger.info('Smartcampus database connection closed');
      }
      
      this.isConnected = false;
    } catch (error) {
      logger.error('Error closing database connections', { error: error.message });
      throw error;
    }
  }
}

module.exports = DatabaseManager;
