<template>
  <div class="app-header" :class="{ 'header-transparent': transparent }">
    <van-nav-bar
      :title="title"
      :left-text="leftText"
      :right-text="rightText"
      :left-arrow="showBack"
      :fixed="fixed"
      :placeholder="placeholder"
      :z-index="zIndex"
      @click-left="handleLeftClick"
      @click-right="handleRightClick"
    >
      <!-- 左侧插槽 -->
      <template v-if="$slots.left" #left>
        <slot name="left" />
      </template>
      
      <!-- 标题插槽 -->
      <template v-if="$slots.title" #title>
        <slot name="title" />
      </template>
      
      <!-- 右侧插槽 -->
      <template v-if="$slots.right" #right>
        <slot name="right" />
      </template>
    </van-nav-bar>
    
    <!-- 搜索栏 -->
    <div v-if="showSearch" class="header-search">
      <van-search
        v-model="searchValue"
        :placeholder="searchPlaceholder"
        :show-action="showSearchAction"
        @search="handleSearch"
        @cancel="handleSearchCancel"
        @clear="handleSearchClear"
      >
        <template v-if="$slots.searchAction" #action>
          <slot name="searchAction" />
        </template>
      </van-search>
    </div>
    
    <!-- 标签页 -->
    <div v-if="showTabs" class="header-tabs">
      <van-tabs
        v-model:active="activeTab"
        :sticky="stickyTabs"
        @change="handleTabChange"
      >
        <van-tab
          v-for="tab in tabs"
          :key="tab.name"
          :title="tab.title"
          :name="tab.name"
          :disabled="tab.disabled"
        />
      </van-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Props
const props = defineProps({
  // 基础属性
  title: {
    type: String,
    default: ''
  },
  leftText: {
    type: String,
    default: ''
  },
  rightText: {
    type: String,
    default: ''
  },
  showBack: {
    type: Boolean,
    default: false
  },
  
  // 样式属性
  transparent: {
    type: Boolean,
    default: false
  },
  fixed: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: Number,
    default: 1
  },
  
  // 搜索功能
  showSearch: {
    type: Boolean,
    default: false
  },
  searchPlaceholder: {
    type: String,
    default: '请输入搜索关键词'
  },
  showSearchAction: {
    type: Boolean,
    default: true
  },
  
  // 标签页功能
  showTabs: {
    type: Boolean,
    default: false
  },
  tabs: {
    type: Array,
    default: () => []
  },
  stickyTabs: {
    type: Boolean,
    default: false
  },
  
  // 自定义行为
  customBack: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'left-click',
  'right-click',
  'search',
  'search-cancel',
  'search-clear',
  'tab-change'
])

// 响应式数据
const searchValue = ref('')
const activeTab = ref(props.tabs[0]?.name || '')

// 方法
const handleLeftClick = () => {
  if (props.customBack) {
    emit('left-click')
  } else if (props.showBack) {
    router.back()
  } else {
    emit('left-click')
  }
}

const handleRightClick = () => {
  emit('right-click')
}

const handleSearch = (value) => {
  emit('search', value)
}

const handleSearchCancel = () => {
  searchValue.value = ''
  emit('search-cancel')
}

const handleSearchClear = () => {
  emit('search-clear')
}

const handleTabChange = (name) => {
  emit('tab-change', name)
}

// 暴露方法给父组件
defineExpose({
  searchValue,
  activeTab,
  clearSearch: () => {
    searchValue.value = ''
  },
  setActiveTab: (name) => {
    activeTab.value = name
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.app-header {
  &.header-transparent {
    :deep(.van-nav-bar) {
      background: transparent;
      
      .van-nav-bar__title {
        color: white;
      }
      
      .van-nav-bar__left,
      .van-nav-bar__right {
        color: white;
      }
    }
  }
  
  .header-search {
    background: white;
    padding: 0 var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    
    :deep(.van-search) {
      padding: var(--spacing-sm) 0;
    }
  }
  
  .header-tabs {
    background: white;
    border-bottom: 1px solid var(--border-color);
    
    :deep(.van-tabs__nav) {
      padding: 0 var(--spacing-md);
    }
  }
}

// 自定义导航栏样式
:deep(.van-nav-bar) {
  .van-nav-bar__title {
    font-weight: var(--font-weight-bold);
  }
  
  .van-nav-bar__left,
  .van-nav-bar__right {
    font-size: var(--font-size-md);
  }
}
</style>
