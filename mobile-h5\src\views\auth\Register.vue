<template>
  <div class="register-page">
    <!-- 头部 -->
    <van-nav-bar
      title="注册账号"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 注册表单 -->
    <div class="register-form">
      <van-form @submit="handleRegister">
        <!-- 步骤指示器 -->
        <van-steps :active="currentStep" class="register-steps">
          <van-step>基本信息</van-step>
          <van-step>验证身份</van-step>
          <van-step>设置密码</van-step>
        </van-steps>

        <!-- 第一步：基本信息 -->
        <div v-show="currentStep === 0" class="step-content">
          <van-field
            v-model="form.name"
            name="name"
            label="姓名"
            placeholder="请输入真实姓名"
            left-icon="user-o"
            :rules="[
              { required: true, message: '请输入姓名' },
              { validator: validateName, message: '请输入正确的姓名格式' }
            ]"
            clearable
          />
          
          <van-field
            v-model="form.studentId"
            name="studentId"
            label="学号"
            placeholder="请输入学号"
            left-icon="card"
            :rules="[
              { required: true, message: '请输入学号' },
              { validator: validateStudentId, message: '请输入正确的学号格式' }
            ]"
            clearable
          />
          
          <van-field
            v-model="form.phone"
            name="phone"
            label="手机号"
            placeholder="请输入手机号"
            left-icon="phone-o"
            :rules="[
              { required: true, message: '请输入手机号' },
              { validator: validatePhone, message: '请输入正确的手机号' }
            ]"
            clearable
          />
          
          <van-field
            v-model="form.email"
            name="email"
            label="邮箱"
            placeholder="请输入邮箱地址"
            left-icon="envelop-o"
            :rules="[
              { required: true, message: '请输入邮箱' },
              { validator: validateEmail, message: '请输入正确的邮箱格式' }
            ]"
            clearable
          />
        </div>

        <!-- 第二步：验证身份 -->
        <div v-show="currentStep === 1" class="step-content">
          <van-tabs v-model:active="verifyType" class="verify-tabs">
            <van-tab title="手机验证" name="phone">
              <div class="verify-content">
                <p class="verify-tip">
                  验证码将发送至：{{ form.phone }}
                </p>
                
                <van-field
                  v-model="form.phoneCode"
                  name="phoneCode"
                  label="验证码"
                  placeholder="请输入手机验证码"
                  left-icon="shield-o"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                  clearable
                >
                  <template #button>
                    <van-button
                      size="small"
                      type="primary"
                      :disabled="phoneCountdown > 0"
                      :loading="sendingPhoneCode"
                      @click="sendPhoneCode"
                    >
                      {{ phoneCountdown > 0 ? `${phoneCountdown}s` : '发送验证码' }}
                    </van-button>
                  </template>
                </van-field>
              </div>
            </van-tab>
            
            <van-tab title="邮箱验证" name="email">
              <div class="verify-content">
                <p class="verify-tip">
                  验证码将发送至：{{ form.email }}
                </p>
                
                <van-field
                  v-model="form.emailCode"
                  name="emailCode"
                  label="验证码"
                  placeholder="请输入邮箱验证码"
                  left-icon="shield-o"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                  clearable
                >
                  <template #button>
                    <van-button
                      size="small"
                      type="primary"
                      :disabled="emailCountdown > 0"
                      :loading="sendingEmailCode"
                      @click="sendEmailCode"
                    >
                      {{ emailCountdown > 0 ? `${emailCountdown}s` : '发送验证码' }}
                    </van-button>
                  </template>
                </van-field>
              </div>
            </van-tab>
          </van-tabs>
        </div>

        <!-- 第三步：设置密码 -->
        <div v-show="currentStep === 2" class="step-content">
          <van-field
            v-model="form.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请设置登录密码"
            left-icon="lock"
            :rules="[
              { required: true, message: '请输入密码' },
              { validator: validatePassword, message: '密码强度不够' }
            ]"
            clearable
          />
          
          <van-field
            v-model="form.confirmPassword"
            type="password"
            name="confirmPassword"
            label="确认密码"
            placeholder="请再次输入密码"
            left-icon="lock"
            :rules="[
              { required: true, message: '请确认密码' },
              { validator: validateConfirmPassword, message: '两次输入的密码不一致' }
            ]"
            clearable
          />
          
          <!-- 密码强度指示器 -->
          <div class="password-strength">
            <div class="strength-label">密码强度：</div>
            <div class="strength-bar">
              <div 
                class="strength-fill"
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthWidth }"
              ></div>
            </div>
            <div class="strength-text">{{ passwordStrengthText }}</div>
          </div>
          
          <!-- 服务条款 -->
          <div class="terms-agreement">
            <van-checkbox v-model="form.agreeTerms">
              我已阅读并同意
              <a href="#" @click.prevent="showTerms">《用户服务协议》</a>
              和
              <a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
            </van-checkbox>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <van-button
            v-if="currentStep > 0"
            round
            size="large"
            @click="prevStep"
          >
            上一步
          </van-button>
          
          <van-button
            v-if="currentStep < 2"
            round
            block
            type="primary"
            size="large"
            @click="nextStep"
          >
            下一步
          </van-button>
          
          <van-button
            v-else
            round
            block
            type="primary"
            native-type="submit"
            size="large"
            :loading="loading"
            :disabled="!form.agreeTerms"
          >
            完成注册
          </van-button>
        </div>
      </van-form>

      <!-- 登录链接 -->
      <div class="login-link">
        <span>已有账号？</span>
        <router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user'
import { useAppStore } from '../../stores/app'
import { validatePhone, validateEmail, validateName, validateStudentId, validatePassword } from '../../utils/validate'
import { authAPI } from '../../api/auth'

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const currentStep = ref(0)
const verifyType = ref('phone')
const loading = ref(false)
const sendingPhoneCode = ref(false)
const sendingEmailCode = ref(false)
const phoneCountdown = ref(0)
const emailCountdown = ref(0)

const form = reactive({
  name: '',
  studentId: '',
  phone: '',
  email: '',
  phoneCode: '',
  emailCode: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 计算属性
const passwordStrength = computed(() => {
  return validatePassword(form.password)
})

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value.strength
  return {
    'strength-weak': strength === 'weak',
    'strength-medium': strength === 'medium',
    'strength-strong': strength === 'strong'
  }
})

const passwordStrengthWidth = computed(() => {
  const score = passwordStrength.value.score
  return `${(score / 5) * 100}%`
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value.strength
  const textMap = {
    weak: '弱',
    medium: '中',
    strong: '强'
  }
  return textMap[strength] || '弱'
})

// 方法
const validateConfirmPassword = (value) => {
  return value === form.password
}

const nextStep = () => {
  if (currentStep.value < 2) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const sendPhoneCode = async () => {
  if (!validatePhone(form.phone) || sendingPhoneCode.value) return
  
  try {
    sendingPhoneCode.value = true
    
    await authAPI.sendSmsCode({
      phone: form.phone,
      type: 'register'
    })
    
    appStore.showToast('验证码已发送', 'success')
    
    // 开始倒计时
    phoneCountdown.value = 60
    const timer = setInterval(() => {
      phoneCountdown.value--
      if (phoneCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    appStore.showToast(error.message || '发送验证码失败', 'error')
  } finally {
    sendingPhoneCode.value = false
  }
}

const sendEmailCode = async () => {
  if (!validateEmail(form.email) || sendingEmailCode.value) return
  
  try {
    sendingEmailCode.value = true
    
    await authAPI.sendEmailCode({
      email: form.email,
      type: 'register'
    })
    
    appStore.showToast('验证码已发送', 'success')
    
    // 开始倒计时
    emailCountdown.value = 60
    const timer = setInterval(() => {
      emailCountdown.value--
      if (emailCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送验证码失败:', error)
    appStore.showToast(error.message || '发送验证码失败', 'error')
  } finally {
    sendingEmailCode.value = false
  }
}

const handleRegister = async () => {
  try {
    loading.value = true
    
    const registerData = {
      name: form.name,
      studentId: form.studentId,
      phone: form.phone,
      email: form.email,
      phoneCode: form.phoneCode,
      emailCode: form.emailCode,
      password: form.password,
      verifyType: verifyType.value
    }
    
    await userStore.register(registerData)
    
    appStore.showToast('注册成功', 'success')
    
    // 跳转到登录页
    router.replace('/login')
    
  } catch (error) {
    console.error('注册失败:', error)
    appStore.showToast(error.message || '注册失败', 'error')
  } finally {
    loading.value = false
  }
}

const showTerms = () => {
  appStore.showToast('服务条款页面开发中', 'info')
}

const showPrivacy = () => {
  appStore.showToast('隐私政策页面开发中', 'info')
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.register-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.register-form {
  padding: var(--spacing-lg);
  
  .register-steps {
    margin-bottom: var(--spacing-xl);
  }
  
  .step-content {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    
    .van-field {
      margin-bottom: var(--spacing-md);
    }
    
    .verify-tabs {
      margin-bottom: var(--spacing-md);
    }
    
    .verify-content {
      padding: var(--spacing-md) 0;
      
      .verify-tip {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-md);
        text-align: center;
      }
    }
    
    .password-strength {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-md);
      font-size: var(--font-size-sm);
      
      .strength-label {
        margin-right: var(--spacing-sm);
        color: var(--text-secondary);
      }
      
      .strength-bar {
        flex: 1;
        height: 4px;
        background: var(--bg-tertiary);
        border-radius: 2px;
        margin-right: var(--spacing-sm);
        overflow: hidden;
        
        .strength-fill {
          height: 100%;
          transition: all 0.3s ease;
          
          &.strength-weak {
            background: var(--danger-color);
          }
          
          &.strength-medium {
            background: var(--warning-color);
          }
          
          &.strength-strong {
            background: var(--success-color);
          }
        }
      }
      
      .strength-text {
        color: var(--text-tertiary);
      }
    }
    
    .terms-agreement {
      margin-top: var(--spacing-lg);
      
      :deep(.van-checkbox__label) {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        
        a {
          color: var(--primary-color);
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    .van-button:first-child {
      flex: 0 0 auto;
      min-width: 80px;
    }
    
    .van-button:last-child {
      flex: 1;
    }
  }
  
  .login-link {
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    
    a {
      color: var(--primary-color);
      text-decoration: none;
      margin-left: var(--spacing-xs);
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
