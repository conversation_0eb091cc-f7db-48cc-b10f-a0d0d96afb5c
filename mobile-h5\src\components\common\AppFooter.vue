<template>
  <div class="app-footer">
    <van-tabbar
      v-model="activeTab"
      :fixed="fixed"
      :placeholder="placeholder"
      :z-index="zIndex"
      :safe-area-inset-bottom="safeArea"
      @change="handleTabChange"
    >
      <van-tabbar-item
        v-for="item in tabItems"
        :key="item.name"
        :name="item.name"
        :icon="item.icon"
        :badge="item.badge"
        :dot="item.dot"
        :to="item.to"
        :replace="item.replace"
        :disabled="item.disabled"
      >
        {{ item.title }}
        
        <!-- 自定义图标 -->
        <template v-if="item.customIcon" #icon="{ active }">
          <component
            :is="item.customIcon"
            :class="{ active }"
            class="custom-icon"
          />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// Props
const props = defineProps({
  // 标签项配置
  items: {
    type: Array,
    default: () => [
      {
        name: 'home',
        title: '首页',
        icon: 'home-o',
        to: '/home'
      },
      {
        name: 'todo',
        title: '待办',
        icon: 'todo-list-o',
        to: '/todo'
      },
      {
        name: 'profile',
        title: '我的',
        icon: 'user-o',
        to: '/profile'
      }
    ]
  },
  
  // 样式属性
  fixed: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: Number,
    default: 1
  },
  safeArea: {
    type: Boolean,
    default: true
  },
  
  // 当前激活项
  modelValue: {
    type: [String, Number],
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const activeTab = ref(props.modelValue)

// 计算属性
const tabItems = computed(() => {
  return props.items.map(item => ({
    name: item.name,
    title: item.title,
    icon: item.icon,
    badge: item.badge || '',
    dot: item.dot || false,
    to: item.to,
    replace: item.replace || false,
    disabled: item.disabled || false,
    customIcon: item.customIcon
  }))
})

// 方法
const handleTabChange = (name) => {
  activeTab.value = name
  emit('update:modelValue', name)
  emit('change', name)
}

// 根据当前路由自动设置激活项
const updateActiveTabByRoute = () => {
  const currentPath = route.path
  const matchedItem = tabItems.value.find(item => {
    if (item.to === currentPath) return true
    if (currentPath.startsWith(item.to) && item.to !== '/') return true
    return false
  })
  
  if (matchedItem && matchedItem.name !== activeTab.value) {
    activeTab.value = matchedItem.name
    emit('update:modelValue', matchedItem.name)
  }
}

// 监听路由变化
watch(() => route.path, updateActiveTabByRoute, { immediate: true })

// 监听外部传入的值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== activeTab.value) {
    activeTab.value = newValue
  }
})

// 暴露方法给父组件
defineExpose({
  activeTab,
  setActiveTab: (name) => {
    activeTab.value = name
    emit('update:modelValue', name)
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.app-footer {
  :deep(.van-tabbar) {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    
    .van-tabbar-item {
      &__text {
        font-size: var(--font-size-xs);
        margin-top: 4px;
      }
      
      &__icon {
        font-size: 20px;
        margin-bottom: 2px;
      }
      
      &--active {
        .van-tabbar-item__text {
          color: var(--primary-color);
        }
        
        .van-tabbar-item__icon {
          color: var(--primary-color);
        }
      }
    }
  }
  
  .custom-icon {
    width: 20px;
    height: 20px;
    transition: color 0.3s ease;
    
    &.active {
      color: var(--primary-color);
    }
  }
}

// 暗色主题适配
[data-theme="dark"] {
  .app-footer {
    :deep(.van-tabbar) {
      background: var(--bg-primary);
      border-top-color: var(--border-color);
    }
  }
}
</style>
