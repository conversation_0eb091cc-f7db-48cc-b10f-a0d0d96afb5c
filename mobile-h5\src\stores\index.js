import { createPinia } from 'pinia'

// 创建 Pinia 实例
const pinia = createPinia()

// 持久化插件
const persistPlugin = (context) => {
  const { store } = context
  
  // 需要持久化的 store
  const persistStores = ['user', 'app']
  
  if (persistStores.includes(store.$id)) {
    // 从 localStorage 恢复状态
    const savedState = localStorage.getItem(`pinia-${store.$id}`)
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState)
        store.$patch(parsedState)
      } catch (error) {
        console.error(`恢复 ${store.$id} 状态失败:`, error)
      }
    }
    
    // 监听状态变化并保存
    store.$subscribe((mutation, state) => {
      try {
        // 过滤敏感信息
        const stateToSave = { ...state }
        if (store.$id === 'user') {
          // 不保存敏感信息
          delete stateToSave.password
        }
        
        localStorage.setItem(`pinia-${store.$id}`, JSON.stringify(stateToSave))
      } catch (error) {
        console.error(`保存 ${store.$id} 状态失败:`, error)
      }
    })
  }
}

// 注册插件
pinia.use(persistPlugin)

export default pinia

// 导出所有 store
export { useAppStore } from './app'
export { useUserStore } from './user'
export { useTodoStore } from './todo'
