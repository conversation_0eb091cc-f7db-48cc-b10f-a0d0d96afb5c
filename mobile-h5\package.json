{"name": "todo-system-mobile-h5", "version": "1.0.0", "description": "入学待办事项系统 - H5移动端", "type": "module", "scripts": {"dev": "node ./node_modules/vite/bin/vite.js", "build": "node ./node_modules/vite/bin/vite.js build", "preview": "node ./node_modules/vite/bin/vite.js preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .js,.vue", "lint:fix": "eslint src --ext .js,.vue --fix", "format": "prettier --write src/**/*.{js,vue,css,scss}", "type-check": "vue-tsc --noEmit"}, "keywords": ["vue3", "vite", "mobile", "h5", "todo", "enrollment", "pwa"], "author": "Todo System Team", "license": "MIT", "dependencies": {"@vueuse/core": "^10.7.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qrcode": "^1.5.3", "vant": "^4.8.0", "vconsole": "^3.15.1", "vue": "^3.3.8", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^4.5.2", "@vitest/ui": "^1.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/test-utils": "^2.4.3", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "sass": "^1.89.1", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.4.19", "vite-plugin-pwa": "^0.17.4", "vite-plugin-windicss": "^1.9.3", "vitest": "^1.0.4", "vue-tsc": "^1.8.25", "windicss": "^3.5.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "iOS >= 10", "Android >= 5"]}