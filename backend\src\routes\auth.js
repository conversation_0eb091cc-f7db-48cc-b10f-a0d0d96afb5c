const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const AuthService = require('../services/AuthService');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 登录速率限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.LOGIN_RATE_LIMIT_MAX) || 5, // 最多5次登录尝试
  skipSuccessfulRequests: true,
  message: {
    success: false,
    code: 4029,
    message: '登录尝试过于频繁，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 学生登录验证规则
const studentLoginValidation = [
  body('studentId')
    .notEmpty()
    .withMessage('学号不能为空')
    .isLength({ min: 8, max: 12 })
    .withMessage('学号长度应为8-12位')
    .matches(/^[0-9]+$/)
    .withMessage('学号只能包含数字'),
  body('language')
    .optional()
    .isIn(['zh-cn', 'en-us'])
    .withMessage('语言设置无效')
];

// 管理员登录验证规则
const adminLoginValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度应为3-50位'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6位'),
  body('language')
    .optional()
    .isIn(['zh-cn', 'en-us'])
    .withMessage('语言设置无效')
];

// 刷新令牌验证规则
const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能为空')
];

/**
 * 学生登录
 * POST /api/v1/auth/student-login
 */
router.post('/student-login', 
  loginLimiter,
  studentLoginValidation,
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const { studentId, language = 'zh-cn' } = req.body;
    const dbManager = req.app.locals.dbManager;

    try {
      const authService = new AuthService(dbManager);
      const result = await authService.studentLogin(studentId, language);

      // 记录登录成功日志
      logger.audit('Student login successful', {
        studentId,
        userId: result.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        language
      });

      res.json({
        success: true,
        code: 1000,
        message: '登录成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // 记录登录失败日志
      logger.security('Student login failed', {
        studentId,
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      if (error.message === '学号不存在或无效') {
        return res.status(400).json({
          success: false,
          code: 4011,
          message: '学号不存在或无效',
          timestamp: new Date().toISOString()
        });
      }

      throw error;
    }
  })
);

/**
 * 管理员登录
 * POST /api/v1/auth/admin-login
 */
router.post('/admin-login',
  loginLimiter,
  adminLoginValidation,
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const { username, password, language = 'zh-cn' } = req.body;
    const dbManager = req.app.locals.dbManager;

    try {
      const authService = new AuthService(dbManager);
      const result = await authService.adminLogin(username, password, language);

      // 记录登录成功日志
      logger.audit('Admin login successful', {
        username,
        userId: result.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        language
      });

      res.json({
        success: true,
        code: 1000,
        message: '登录成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // 记录登录失败日志
      logger.security('Admin login failed', {
        username,
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      if (error.message === '用户名或密码错误') {
        return res.status(401).json({
          success: false,
          code: 4001,
          message: '用户名或密码错误',
          timestamp: new Date().toISOString()
        });
      }

      throw error;
    }
  })
);

/**
 * 刷新访问令牌
 * POST /api/v1/auth/refresh
 */
router.post('/refresh',
  refreshTokenValidation,
  asyncHandler(async (req, res) => {
    // 验证请求参数
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数错误',
        error: {
          type: 'VALIDATION_ERROR',
          details: errors.array().map(error => ({
            field: error.path,
            message: error.msg
          }))
        },
        timestamp: new Date().toISOString()
      });
    }

    const { refreshToken } = req.body;
    const dbManager = req.app.locals.dbManager;

    try {
      const authService = new AuthService(dbManager);
      const result = await authService.refreshToken(refreshToken);

      // 记录令牌刷新日志
      logger.audit('Token refreshed', {
        userId: result.user.id,
        studentId: result.user.studentId,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.json({
        success: true,
        code: 1000,
        message: '令牌刷新成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // 记录令牌刷新失败日志
      logger.security('Token refresh failed', {
        error: error.message,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      if (error.message.includes('token')) {
        return res.status(401).json({
          success: false,
          code: 4001,
          message: '刷新令牌无效或已过期',
          timestamp: new Date().toISOString()
        });
      }

      throw error;
    }
  })
);

/**
 * 退出登录
 * POST /api/v1/auth/logout
 */
router.post('/logout',
  asyncHandler(async (req, res) => {
    const authHeader = req.headers.authorization;
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (token) {
      // 记录退出登录日志
      logger.audit('User logout', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        hasToken: true
      });

      // TODO: 将token加入黑名单 (如果使用Redis)
      // await blacklistToken(token);
    }

    res.json({
      success: true,
      code: 1000,
      message: '退出登录成功',
      timestamp: new Date().toISOString()
    });
  })
);

/**
 * 开发环境测试登录
 * POST /api/v1/auth/test-login
 */
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_TEST_LOGIN === 'true') {
  router.post('/test-login',
    body('testUser').notEmpty().withMessage('测试用户不能为空'),
    asyncHandler(async (req, res) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          code: 4010,
          message: '请求参数错误',
          error: {
            type: 'VALIDATION_ERROR',
            details: errors.array().map(error => ({
              field: error.path,
              message: error.msg
            }))
          },
          timestamp: new Date().toISOString()
        });
      }

      const { testUser, language = 'zh-cn' } = req.body;
      const dbManager = req.app.locals.dbManager;

      try {
        const authService = new AuthService(dbManager);
        const result = await authService.testLogin(testUser, language);

        logger.audit('Test login successful', {
          testUser,
          userId: result.user.id,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });

        res.json({
          success: true,
          code: 1000,
          message: '测试登录成功',
          data: result,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('Test login failed', {
          testUser,
          error: error.message,
          ip: req.ip
        });

        throw error;
      }
    })
  );
}

module.exports = router;
