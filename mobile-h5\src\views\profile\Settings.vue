<template>
  <div class="settings-page">
    <!-- 头部 -->
    <van-nav-bar
      title="设置"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 通用设置 -->
      <div class="settings-group">
        <h3 class="group-title">通用设置</h3>
        <van-cell-group>
          <van-cell title="语言" is-link @click="showLanguagePopup = true">
            <template #value>
              <span>{{ getLanguageText(appStore.locale) }}</span>
            </template>
          </van-cell>
          
          <van-cell title="主题" is-link @click="showThemePopup = true">
            <template #value>
              <span>{{ getThemeText(appStore.theme) }}</span>
            </template>
          </van-cell>
          
          <van-cell title="字体大小" is-link @click="showFontSizePopup = true">
            <template #value>
              <span>{{ getFontSizeText(fontSize) }}</span>
            </template>
          </van-cell>
          
          <van-cell title="自动同步">
            <template #right-icon>
              <van-switch
                v-model="settings.autoSync"
                @change="updateSetting('autoSync', $event)"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 通知设置 -->
      <div class="settings-group">
        <h3 class="group-title">通知设置</h3>
        <van-cell-group>
          <van-cell title="推送通知">
            <template #right-icon>
              <van-switch
                v-model="settings.pushNotification"
                @change="updateSetting('pushNotification', $event)"
              />
            </template>
          </van-cell>
          
          <van-cell title="邮件通知">
            <template #right-icon>
              <van-switch
                v-model="settings.emailNotification"
                @change="updateSetting('emailNotification', $event)"
              />
            </template>
          </van-cell>
          
          <van-cell title="短信通知">
            <template #right-icon>
              <van-switch
                v-model="settings.smsNotification"
                @change="updateSetting('smsNotification', $event)"
              />
            </template>
          </van-cell>
          
          <van-cell title="声音提醒">
            <template #right-icon>
              <van-switch
                v-model="settings.soundEnabled"
                @change="updateSetting('soundEnabled', $event)"
              />
            </template>
          </van-cell>
          
          <van-cell title="震动提醒">
            <template #right-icon>
              <van-switch
                v-model="settings.vibrationEnabled"
                @change="updateSetting('vibrationEnabled', $event)"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 隐私设置 -->
      <div class="settings-group">
        <h3 class="group-title">隐私设置</h3>
        <van-cell-group>
          <van-cell title="数据分析">
            <template #right-icon>
              <van-switch
                v-model="settings.dataAnalytics"
                @change="updateSetting('dataAnalytics', $event)"
              />
            </template>
          </van-cell>
          
          <van-cell title="错误报告">
            <template #right-icon>
              <van-switch
                v-model="settings.errorReporting"
                @change="updateSetting('errorReporting', $event)"
              />
            </template>
          </van-cell>
          
          <van-cell title="个性化推荐">
            <template #right-icon>
              <van-switch
                v-model="settings.personalization"
                @change="updateSetting('personalization', $event)"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 存储设置 -->
      <div class="settings-group">
        <h3 class="group-title">存储设置</h3>
        <van-cell-group>
          <van-cell
            title="缓存大小"
            :value="cacheSize"
            is-link
            @click="showCacheInfo"
          />
          
          <van-cell
            title="清除缓存"
            is-link
            @click="clearCache"
          />
          
          <van-cell
            title="数据备份"
            is-link
            @click="backupData"
          />
          
          <van-cell
            title="数据恢复"
            is-link
            @click="restoreData"
          />
        </van-cell-group>
      </div>

      <!-- 关于 -->
      <div class="settings-group">
        <h3 class="group-title">关于</h3>
        <van-cell-group>
          <van-cell
            title="版本号"
            :value="appStore.appVersion"
          />
          
          <van-cell
            title="检查更新"
            is-link
            @click="checkUpdate"
          />
          
          <van-cell
            title="用户协议"
            is-link
            @click="showTerms"
          />
          
          <van-cell
            title="隐私政策"
            is-link
            @click="showPrivacy"
          />
          
          <van-cell
            title="开源许可"
            is-link
            @click="showLicense"
          />
        </van-cell-group>
      </div>

      <!-- 危险操作 -->
      <div class="settings-group">
        <h3 class="group-title">危险操作</h3>
        <van-cell-group>
          <van-cell
            title="重置设置"
            is-link
            @click="resetSettings"
          />
          
          <van-cell
            title="清除所有数据"
            is-link
            @click="clearAllData"
          />
          
          <van-cell
            title="注销账户"
            is-link
            @click="deleteAccount"
          />
        </van-cell-group>
      </div>
    </div>

    <!-- 语言选择弹窗 -->
    <van-popup v-model:show="showLanguagePopup" position="bottom" round>
      <div class="popup-content">
        <div class="popup-header">
          <h3>选择语言</h3>
        </div>
        <van-radio-group v-model="selectedLanguage" @change="changeLanguage">
          <van-cell-group>
            <van-cell
              v-for="lang in languages"
              :key="lang.value"
              :title="lang.label"
              clickable
              @click="selectedLanguage = lang.value"
            >
              <template #right-icon>
                <van-radio :name="lang.value" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>

    <!-- 主题选择弹窗 -->
    <van-popup v-model:show="showThemePopup" position="bottom" round>
      <div class="popup-content">
        <div class="popup-header">
          <h3>选择主题</h3>
        </div>
        <van-radio-group v-model="selectedTheme" @change="changeTheme">
          <van-cell-group>
            <van-cell
              v-for="theme in themes"
              :key="theme.value"
              :title="theme.label"
              clickable
              @click="selectedTheme = theme.value"
            >
              <template #right-icon>
                <van-radio :name="theme.value" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </van-popup>

    <!-- 字体大小选择弹窗 -->
    <van-popup v-model:show="showFontSizePopup" position="bottom" round>
      <div class="popup-content">
        <div class="popup-header">
          <h3>字体大小</h3>
        </div>
        <div class="font-size-slider">
          <van-slider
            v-model="fontSize"
            :min="12"
            :max="20"
            :step="2"
            @change="changeFontSize"
          />
          <div class="font-size-preview" :style="{ fontSize: fontSize + 'px' }">
            预览文字大小
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '../../stores/app'
import { useUserStore } from '../../stores/user'
import { getStorageSize, clearCache as clearStorageCache } from '../../utils/storage'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 响应式数据
const showLanguagePopup = ref(false)
const showThemePopup = ref(false)
const showFontSizePopup = ref(false)
const selectedLanguage = ref(appStore.locale)
const selectedTheme = ref(appStore.theme)
const fontSize = ref(14)
const cacheSize = ref('0 KB')

const settings = reactive({
  autoSync: true,
  pushNotification: true,
  emailNotification: false,
  smsNotification: false,
  soundEnabled: true,
  vibrationEnabled: true,
  dataAnalytics: true,
  errorReporting: true,
  personalization: true
})

// 配置数据
const languages = [
  { value: 'zh-CN', label: '简体中文' },
  { value: 'en-US', label: 'English' }
]

const themes = [
  { value: 'light', label: '浅色' },
  { value: 'dark', label: '深色' },
  { value: 'auto', label: '跟随系统' }
]

// 方法
const updateSetting = (key, value) => {
  settings[key] = value
  // 保存设置到本地存储
  localStorage.setItem(`setting_${key}`, JSON.stringify(value))
  appStore.showToast('设置已保存', 'success')
}

const changeLanguage = (value) => {
  appStore.setLocale(value)
  showLanguagePopup.value = false
  appStore.showToast('语言设置已更新', 'success')
}

const changeTheme = (value) => {
  appStore.setTheme(value)
  showThemePopup.value = false
  appStore.showToast('主题设置已更新', 'success')
}

const changeFontSize = (value) => {
  document.documentElement.style.fontSize = value + 'px'
  localStorage.setItem('fontSize', value)
  showFontSizePopup.value = false
  appStore.showToast('字体大小已更新', 'success')
}

const showCacheInfo = () => {
  const size = getStorageSize()
  appStore.showDialog({
    title: '缓存信息',
    message: `本地存储: ${size.localStorage}\n会话存储: ${size.sessionStorage}\n总计: ${size.total}`
  })
}

const clearCache = async () => {
  try {
    await appStore.showDialog({
      title: '清除缓存',
      message: '确定要清除所有缓存数据吗？'
    })
    
    clearStorageCache()
    cacheSize.value = '0 KB'
    appStore.showToast('缓存已清除', 'success')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除缓存失败:', error)
    }
  }
}

const backupData = () => {
  appStore.showToast('数据备份功能开发中', 'info')
}

const restoreData = () => {
  appStore.showToast('数据恢复功能开发中', 'info')
}

const checkUpdate = () => {
  appStore.showToast('已是最新版本', 'success')
}

const showTerms = () => {
  appStore.showToast('用户协议页面开发中', 'info')
}

const showPrivacy = () => {
  appStore.showToast('隐私政策页面开发中', 'info')
}

const showLicense = () => {
  appStore.showToast('开源许可页面开发中', 'info')
}

const resetSettings = async () => {
  try {
    await appStore.showDialog({
      title: '重置设置',
      message: '确定要重置所有设置为默认值吗？'
    })
    
    // 重置设置
    Object.keys(settings).forEach(key => {
      settings[key] = true
      localStorage.removeItem(`setting_${key}`)
    })
    
    appStore.showToast('设置已重置', 'success')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置设置失败:', error)
    }
  }
}

const clearAllData = async () => {
  try {
    await appStore.showDialog({
      title: '清除所有数据',
      message: '此操作将清除所有本地数据，包括待办事项、设置等，且不可恢复！'
    })
    
    // 清除所有数据
    localStorage.clear()
    sessionStorage.clear()
    
    appStore.showToast('所有数据已清除', 'success')
    
    // 跳转到登录页
    setTimeout(() => {
      router.replace('/login')
    }, 1000)
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除数据失败:', error)
    }
  }
}

const deleteAccount = async () => {
  try {
    await appStore.showDialog({
      title: '注销账户',
      message: '此操作将永久删除您的账户和所有数据，且不可恢复！'
    })
    
    // 注销账户
    await userStore.logout()
    appStore.showToast('账户已注销', 'success')
    router.replace('/login')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('注销账户失败:', error)
      appStore.showToast('注销失败', 'error')
    }
  }
}

// 工具方法
const getLanguageText = (locale) => {
  const lang = languages.find(l => l.value === locale)
  return lang?.label || '简体中文'
}

const getThemeText = (theme) => {
  const themeItem = themes.find(t => t.value === theme)
  return themeItem?.label || '浅色'
}

const getFontSizeText = (size) => {
  if (size <= 12) return '小'
  if (size <= 16) return '中'
  return '大'
}

// 生命周期
onMounted(() => {
  // 加载设置
  Object.keys(settings).forEach(key => {
    const saved = localStorage.getItem(`setting_${key}`)
    if (saved !== null) {
      settings[key] = JSON.parse(saved)
    }
  })
  
  // 加载字体大小
  const savedFontSize = localStorage.getItem('fontSize')
  if (savedFontSize) {
    fontSize.value = parseInt(savedFontSize)
  }
  
  // 计算缓存大小
  const size = getStorageSize()
  cacheSize.value = size.total
})
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;
@use '@/styles/mixins.scss' as *;

.settings-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.settings-content {
  padding: var(--spacing-md);
  
  .settings-group {
    margin-bottom: var(--spacing-lg);
    
    .group-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-md) var(--spacing-md);
    }
    
    .van-cell-group {
      border-radius: var(--border-radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-light);
    }
  }
}

.popup-content {
  padding: var(--spacing-lg);
  
  .popup-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
    }
  }
  
  .font-size-slider {
    .van-slider {
      margin-bottom: var(--spacing-lg);
    }
    
    .font-size-preview {
      text-align: center;
      color: var(--text-primary);
      transition: font-size 0.3s ease;
    }
  }
}
</style>
