<template>
  <div class="todo-card" :class="cardClass" @click="handleClick">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="header-left">
        <van-checkbox
          v-if="showCheckbox"
          :model-value="todo.completed"
          @click.stop="handleToggle"
        />
        
        <div class="todo-priority" :class="todo.priority">
          {{ getPriorityText(todo.priority) }}
        </div>
      </div>
      
      <div class="header-right">
        <van-icon
          v-if="showActions"
          name="more-o"
          @click.stop="showActionSheet = true"
        />
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-content">
      <h3 class="todo-title" :class="{ completed: todo.completed }">
        {{ todo.title }}
      </h3>
      
      <p v-if="todo.description && showDescription" class="todo-description">
        {{ todo.description }}
      </p>
      
      <!-- 进度条 -->
      <div v-if="todo.progress !== undefined && showProgress" class="todo-progress">
        <div class="progress-header">
          <span class="progress-label">进度</span>
          <span class="progress-value">{{ todo.progress }}%</span>
        </div>
        <van-progress
          :percentage="todo.progress"
          stroke-width="6"
          :color="getProgressColor(todo.progress)"
        />
      </div>
    </div>
    
    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="footer-left">
        <span class="todo-category">
          {{ getCategoryText(todo.category) }}
        </span>
        
        <span v-if="todo.attachments?.length" class="todo-attachments">
          <van-icon name="attachment" />
          {{ todo.attachments.length }}
        </span>
        
        <span v-if="todo.comments?.length" class="todo-comments">
          <van-icon name="comment-o" />
          {{ todo.comments.length }}
        </span>
      </div>
      
      <div class="footer-right">
        <span v-if="todo.dueDate" class="todo-due-date" :class="getDueDateClass(todo)">
          <van-icon name="clock-o" />
          {{ formatDueDate(todo.dueDate) }}
        </span>
      </div>
    </div>
    
    <!-- 操作面板 -->
    <van-action-sheet
      v-model:show="showActionSheet"
      :actions="actionSheetActions"
      @select="handleActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { utils } from '../../utils/global'

// Props
const props = defineProps({
  // 待办数据
  todo: {
    type: Object,
    required: true
  },
  
  // 显示控制
  showCheckbox: {
    type: Boolean,
    default: true
  },
  showDescription: {
    type: Boolean,
    default: true
  },
  showProgress: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  
  // 样式控制
  size: {
    type: String,
    default: 'normal', // small, normal, large
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  },
  shadow: {
    type: String,
    default: 'light', // none, light, medium, heavy
    validator: (value) => ['none', 'light', 'medium', 'heavy'].includes(value)
  },
  rounded: {
    type: Boolean,
    default: true
  },
  
  // 交互控制
  clickable: {
    type: Boolean,
    default: true
  },
  selectable: {
    type: Boolean,
    default: false
  },
  selected: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'click',
  'toggle',
  'edit',
  'delete',
  'share',
  'archive'
])

// 响应式数据
const showActionSheet = ref(false)

// 计算属性
const cardClass = computed(() => {
  return {
    [`todo-card--${props.size}`]: true,
    [`todo-card--shadow-${props.shadow}`]: props.shadow !== 'none',
    'todo-card--rounded': props.rounded,
    'todo-card--clickable': props.clickable,
    'todo-card--completed': props.todo.completed,
    'todo-card--selected': props.selected,
    'todo-card--overdue': isOverdue.value
  }
})

const isOverdue = computed(() => {
  if (!props.todo.dueDate || props.todo.completed) return false
  return new Date(props.todo.dueDate) < new Date()
})

const actionSheetActions = computed(() => {
  const actions = [
    { name: 'edit', text: '编辑', icon: 'edit' },
    { name: 'share', text: '分享', icon: 'share' }
  ]
  
  if (!props.todo.completed) {
    actions.push({ name: 'archive', text: '归档', icon: 'archive' })
  }
  
  actions.push({ 
    name: 'delete', 
    text: '删除', 
    icon: 'delete-o',
    color: '#ee0a24'
  })
  
  return actions
})

// 方法
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.todo)
  }
}

const handleToggle = () => {
  emit('toggle', props.todo)
}

const handleActionSelect = (action) => {
  emit(action.name, props.todo)
  showActionSheet.value = false
}

// 工具方法
const getPriorityText = (priority) => {
  const map = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return map[priority] || '中'
}

const getCategoryText = (category) => {
  const map = {
    academic: '学术',
    administrative: '行政',
    accommodation: '住宿',
    financial: '财务',
    health: '健康',
    social: '社交',
    other: '其他'
  }
  return map[category] || '其他'
}

const formatDueDate = (date) => {
  if (!date) return ''
  
  const now = new Date()
  const due = new Date(date)
  const diffDays = Math.ceil((due - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return `逾期${Math.abs(diffDays)}天`
  } else if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '明天'
  } else if (diffDays <= 7) {
    return `${diffDays}天后`
  } else {
    return utils.formatDate(date, 'MM-DD')
  }
}

const getDueDateClass = (todo) => {
  if (!todo.dueDate || todo.completed) return ''
  
  const now = new Date()
  const due = new Date(todo.dueDate)
  const diffHours = (due - now) / (1000 * 60 * 60)
  
  if (diffHours < 0) return 'overdue'
  if (diffHours < 24) return 'urgent'
  if (diffHours < 72) return 'warning'
  
  return ''
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#ee0a24'
  if (progress < 70) return '#ff976a'
  return '#07c160'
}
</script>

<style lang="scss" scoped>
.todo-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  
  // 尺寸变体
  &--small {
    padding: var(--spacing-sm);
    
    .todo-title {
      font-size: var(--font-size-sm);
    }
  }
  
  &--normal {
    padding: var(--spacing-md);
  }
  
  &--large {
    padding: var(--spacing-lg);
    
    .todo-title {
      font-size: var(--font-size-lg);
    }
  }
  
  // 阴影变体
  &--shadow-light {
    box-shadow: var(--shadow-light);
  }
  
  &--shadow-medium {
    box-shadow: var(--shadow-medium);
  }
  
  &--shadow-heavy {
    box-shadow: var(--shadow-heavy);
  }
  
  // 圆角
  &--rounded {
    border-radius: var(--border-radius-lg);
  }
  
  // 可点击
  &--clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  // 已完成状态
  &--completed {
    opacity: 0.7;
    background: var(--bg-secondary);
  }
  
  // 选中状态
  &--selected {
    border-color: var(--primary-color);
    background: rgba(25, 137, 250, 0.05);
  }
  
  // 逾期状态
  &--overdue {
    border-left: 4px solid var(--danger-color);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    
    .header-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
    
    .todo-priority {
      padding: 2px 6px;
      border-radius: 4px;
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      
      &.low {
        background: rgba(7, 193, 96, 0.1);
        color: var(--success-color);
      }
      
      &.medium {
        background: rgba(255, 151, 106, 0.1);
        color: var(--warning-color);
      }
      
      &.high,
      &.urgent {
        background: rgba(238, 10, 36, 0.1);
        color: var(--danger-color);
      }
    }
    
    .header-right {
      .van-icon {
        color: var(--text-tertiary);
        cursor: pointer;
        
        &:hover {
          color: var(--text-secondary);
        }
      }
    }
  }
  
  .card-content {
    margin-bottom: var(--spacing-md);
    
    .todo-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      margin: 0 0 var(--spacing-xs) 0;
      line-height: 1.4;
      
      &.completed {
        text-decoration: line-through;
        color: var(--text-tertiary);
      }
    }
    
    .todo-description {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin: 0 0 var(--spacing-sm) 0;
      line-height: 1.5;
      @include text-ellipsis(2);
    }
    
    .todo-progress {
      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
        
        .progress-label {
          font-size: var(--font-size-xs);
          color: var(--text-secondary);
        }
        
        .progress-value {
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          color: var(--text-primary);
        }
      }
    }
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    
    .footer-left {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      
      .todo-category {
        background: var(--primary-color);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
      }
      
      .todo-attachments,
      .todo-comments {
        display: flex;
        align-items: center;
        gap: 2px;
      }
    }
    
    .footer-right {
      .todo-due-date {
        display: flex;
        align-items: center;
        gap: 2px;
        
        &.warning {
          color: var(--warning-color);
        }
        
        &.urgent {
          color: var(--warning-color);
          font-weight: var(--font-weight-medium);
        }
        
        &.overdue {
          color: var(--danger-color);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .todo-card {
    &--normal {
      padding: var(--spacing-sm);
    }
    
    .card-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }
  }
}
</style>
