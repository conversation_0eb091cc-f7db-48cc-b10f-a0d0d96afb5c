@echo off
REM 入学待办事项系统部署脚本 (Windows版本)
REM 作者: Todo System Team
REM 创建时间: 2024-12-05

setlocal enabledelayedexpansion

REM 设置颜色
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 检查命令是否存在
:check_command
where %1 >nul 2>&1
if errorlevel 1 (
    call :log_error "%1 命令不存在，请先安装"
    exit /b 1
)
goto :eof

REM 检查Node.js版本
:check_node_version
for /f "tokens=1 delims=v" %%i in ('node -v') do set node_version=%%i
for /f "tokens=1 delims=." %%i in ("%node_version%") do set major_version=%%i
if %major_version% LSS 18 (
    call :log_error "Node.js版本过低，需要 >= 18，当前版本: %node_version%"
    exit /b 1
)
call :log_success "Node.js版本检查通过: %node_version%"
goto :eof

REM 检查系统环境
:check_environment
call :log_info "检查系统环境..."

REM 检查必需的命令
call :check_command node
if errorlevel 1 exit /b 1

call :check_command npm
if errorlevel 1 exit /b 1

call :check_command git
if errorlevel 1 exit /b 1

REM 检查Node.js版本
call :check_node_version
if errorlevel 1 exit /b 1

call :log_success "系统环境检查完成"
goto :eof

REM 创建必要的目录
:create_directories
call :log_info "创建必要的目录..."

if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
if not exist "monitoring_data" mkdir "monitoring_data"
if not exist "scripts\python\analysis_output" mkdir "scripts\python\analysis_output"

call :log_success "目录创建完成"
goto :eof

REM 安装后端依赖
:install_backend_dependencies
call :log_info "安装后端依赖..."

cd backend
if not exist "package.json" (
    call :log_error "backend\package.json 不存在"
    exit /b 1
)

npm ci --production
if errorlevel 1 (
    call :log_error "后端依赖安装失败"
    exit /b 1
)

cd ..
call :log_success "后端依赖安装完成"
goto :eof

REM 安装前端依赖
:install_frontend_dependencies
call :log_info "安装前端依赖..."

REM 安装H5移动端依赖
if exist "mobile-h5" (
    call :log_info "安装H5移动端依赖..."
    cd mobile-h5
    npm ci
    if errorlevel 1 (
        call :log_error "H5移动端依赖安装失败"
        exit /b 1
    )
    cd ..
)

REM 安装管理后台依赖
if exist "admin-web" (
    call :log_info "安装管理后台依赖..."
    cd admin-web
    npm ci
    if errorlevel 1 (
        call :log_error "管理后台依赖安装失败"
        exit /b 1
    )
    cd ..
)

call :log_success "前端依赖安装完成"
goto :eof

REM 配置环境变量
:setup_environment
call :log_info "配置环境变量..."

if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env"
        call :log_warning "已创建 backend\.env 文件，请手动配置数据库连接等信息"
    ) else (
        call :log_error "backend\.env.example 文件不存在"
        exit /b 1
    )
) else (
    call :log_info "backend\.env 文件已存在"
)

call :log_success "环境变量配置完成"
goto :eof

REM 构建前端项目
:build_frontend
call :log_info "构建前端项目..."

REM 构建H5移动端
if exist "mobile-h5" (
    call :log_info "构建H5移动端..."
    cd mobile-h5
    npm run build
    if errorlevel 1 (
        call :log_error "H5移动端构建失败"
        exit /b 1
    )
    cd ..
    call :log_success "H5移动端构建完成"
)

REM 构建管理后台
if exist "admin-web" (
    call :log_info "构建管理后台..."
    cd admin-web
    npm run build
    if errorlevel 1 (
        call :log_error "管理后台构建失败"
        exit /b 1
    )
    cd ..
    call :log_success "管理后台构建完成"
)
goto :eof

REM 启动服务
:start_services
call :log_info "启动服务..."

REM 检查PM2是否安装
where pm2 >nul 2>&1
if errorlevel 1 (
    call :log_info "安装PM2..."
    npm install -g pm2
    if errorlevel 1 (
        call :log_error "PM2安装失败"
        exit /b 1
    )
)

REM 停止现有服务
pm2 stop todo-system >nul 2>&1
pm2 delete todo-system >nul 2>&1

REM 启动后端服务
cd backend
pm2 start server.js --name "todo-system"
if errorlevel 1 (
    call :log_error "服务启动失败"
    exit /b 1
)
cd ..

REM 保存PM2配置
pm2 save

call :log_success "服务启动完成"
goto :eof

REM 运行健康检查
:health_check
call :log_info "运行健康检查..."

REM 等待服务启动
timeout /t 5 /nobreak >nul

REM 检查后端服务
curl -s -o nul -w "%%{http_code}" "http://localhost:3001/health" > temp_response.txt 2>nul
if exist temp_response.txt (
    set /p response=<temp_response.txt
    del temp_response.txt
    if "!response!"=="200" (
        call :log_success "后端服务健康检查通过"
    ) else (
        call :log_error "后端服务健康检查失败 (HTTP !response!)"
        exit /b 1
    )
) else (
    call :log_error "无法连接到后端服务"
    exit /b 1
)
goto :eof

REM 显示部署信息
:show_deployment_info
call :log_success "部署完成！"
echo.
echo === 部署信息 ===
echo 后端服务: http://localhost:3001
echo 健康检查: http://localhost:3001/health
echo API文档: http://localhost:3001/api/v1
echo.
echo === 管理命令 ===
echo 查看服务状态: pm2 status
echo 查看日志: pm2 logs todo-system
echo 重启服务: pm2 restart todo-system
echo 停止服务: pm2 stop todo-system
echo.
echo === 配置文件 ===
echo 后端配置: backend\.env
echo 数据库脚本: scripts\init-database.sql
echo.
echo === 日志文件 ===
echo 应用日志: logs\
echo PM2日志: %USERPROFILE%\.pm2\logs\
echo.
call :log_warning "请确保已正确配置 backend\.env 文件中的数据库连接信息"
goto :eof

REM 主函数
:main
call :log_info "开始部署入学待办事项系统..."

REM 解析命令行参数
set SKIP_DEPS=false
set SKIP_BUILD=false

:parse_args
if "%1"=="--skip-deps" (
    set SKIP_DEPS=true
    shift
    goto parse_args
)
if "%1"=="--skip-build" (
    set SKIP_BUILD=true
    shift
    goto parse_args
)
if "%1"=="--help" (
    echo 用法: %0 [选项]
    echo 选项:
    echo   --skip-deps   跳过依赖安装
    echo   --skip-build  跳过前端构建
    echo   --help        显示帮助信息
    exit /b 0
)
if not "%1"=="" (
    call :log_error "未知选项: %1"
    exit /b 1
)

REM 执行部署步骤
call :check_environment
if errorlevel 1 exit /b 1

call :create_directories
if errorlevel 1 exit /b 1

call :setup_environment
if errorlevel 1 exit /b 1

if "%SKIP_DEPS%"=="false" (
    call :install_backend_dependencies
    if errorlevel 1 exit /b 1
    
    call :install_frontend_dependencies
    if errorlevel 1 exit /b 1
)

if "%SKIP_BUILD%"=="false" (
    call :build_frontend
    if errorlevel 1 exit /b 1
)

call :start_services
if errorlevel 1 exit /b 1

call :health_check
if errorlevel 1 exit /b 1

call :show_deployment_info

goto :eof

REM 执行主函数
call :main %*
