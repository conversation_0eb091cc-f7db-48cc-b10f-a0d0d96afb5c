import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { todoAPI } from '../api/todo'

export const useTodoStore = defineStore('todo', () => {
  // 状态
  const todos = ref([])
  const currentTodo = ref(null)
  const loading = ref(false)
  const categories = ref([])
  const filters = ref({
    status: 'all', // all, pending, completed, overdue
    category: 'all',
    priority: 'all', // all, high, medium, low
    search: ''
  })
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    hasMore: true
  })
  
  // 计算属性
  const filteredTodos = computed(() => {
    let result = [...todos.value]
    
    // 状态过滤
    if (filters.value.status !== 'all') {
      result = result.filter(todo => {
        switch (filters.value.status) {
          case 'pending':
            return !todo.completed && !isOverdue(todo)
          case 'completed':
            return todo.completed
          case 'overdue':
            return !todo.completed && isOverdue(todo)
          default:
            return true
        }
      })
    }
    
    // 分类过滤
    if (filters.value.category !== 'all') {
      result = result.filter(todo => todo.category === filters.value.category)
    }
    
    // 优先级过滤
    if (filters.value.priority !== 'all') {
      result = result.filter(todo => todo.priority === filters.value.priority)
    }
    
    // 搜索过滤
    if (filters.value.search) {
      const searchTerm = filters.value.search.toLowerCase()
      result = result.filter(todo => 
        todo.title.toLowerCase().includes(searchTerm) ||
        todo.description.toLowerCase().includes(searchTerm)
      )
    }
    
    return result
  })
  
  const todoStats = computed(() => {
    const total = todos.value.length
    const completed = todos.value.filter(todo => todo.completed).length
    const pending = todos.value.filter(todo => !todo.completed && !isOverdue(todo)).length
    const overdue = todos.value.filter(todo => !todo.completed && isOverdue(todo)).length
    
    return {
      total,
      completed,
      pending,
      overdue,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
    }
  })
  
  const urgentTodos = computed(() => {
    return todos.value.filter(todo => 
      !todo.completed && 
      (todo.priority === 'high' || isOverdue(todo))
    ).slice(0, 5)
  })
  
  // 工具函数
  const isOverdue = (todo) => {
    if (!todo.dueDate) return false
    return new Date(todo.dueDate) < new Date() && !todo.completed
  }
  
  // 方法
  const fetchTodos = async (refresh = false) => {
    try {
      loading.value = true
      
      if (refresh) {
        pagination.value.page = 1
        todos.value = []
      }
      
      const params = {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        ...filters.value
      }
      
      const response = await todoAPI.getTodos(params)
      const { data, total, hasMore } = response.data
      
      if (refresh) {
        todos.value = data
      } else {
        todos.value.push(...data)
      }
      
      pagination.value.total = total
      pagination.value.hasMore = hasMore
      
      return response
    } catch (error) {
      console.error('获取待办事项失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const loadMoreTodos = async () => {
    if (!pagination.value.hasMore || loading.value) {
      return
    }
    
    pagination.value.page += 1
    await fetchTodos(false)
  }
  
  const getTodoById = async (id) => {
    try {
      // 先从本地查找
      const localTodo = todos.value.find(todo => todo.id === id)
      if (localTodo) {
        currentTodo.value = localTodo
        return localTodo
      }
      
      // 从服务器获取
      const response = await todoAPI.getTodoById(id)
      currentTodo.value = response.data
      
      return response.data
    } catch (error) {
      console.error('获取待办事项详情失败:', error)
      throw error
    }
  }
  
  const createTodo = async (todoData) => {
    try {
      const response = await todoAPI.createTodo(todoData)
      const newTodo = response.data
      
      // 添加到本地列表
      todos.value.unshift(newTodo)
      
      console.log('待办事项创建成功')
      return response
    } catch (error) {
      console.error('创建待办事项失败:', error)
      throw error
    }
  }
  
  const updateTodo = async (id, todoData) => {
    try {
      const response = await todoAPI.updateTodo(id, todoData)
      const updatedTodo = response.data
      
      // 更新本地列表
      const index = todos.value.findIndex(todo => todo.id === id)
      if (index !== -1) {
        todos.value[index] = updatedTodo
      }
      
      // 更新当前待办事项
      if (currentTodo.value?.id === id) {
        currentTodo.value = updatedTodo
      }
      
      console.log('待办事项更新成功')
      return response
    } catch (error) {
      console.error('更新待办事项失败:', error)
      throw error
    }
  }
  
  const deleteTodo = async (id) => {
    try {
      await todoAPI.deleteTodo(id)
      
      // 从本地列表移除
      const index = todos.value.findIndex(todo => todo.id === id)
      if (index !== -1) {
        todos.value.splice(index, 1)
      }
      
      // 清除当前待办事项
      if (currentTodo.value?.id === id) {
        currentTodo.value = null
      }
      
      console.log('待办事项删除成功')
    } catch (error) {
      console.error('删除待办事项失败:', error)
      throw error
    }
  }
  
  const toggleTodoStatus = async (id) => {
    try {
      const todo = todos.value.find(t => t.id === id)
      if (!todo) return
      
      const response = await todoAPI.toggleTodoStatus(id)
      const updatedTodo = response.data
      
      // 更新本地状态
      const index = todos.value.findIndex(t => t.id === id)
      if (index !== -1) {
        todos.value[index] = updatedTodo
      }
      
      console.log('待办事项状态更新成功')
      return response
    } catch (error) {
      console.error('更新待办事项状态失败:', error)
      throw error
    }
  }
  
  const fetchCategories = async () => {
    try {
      const response = await todoAPI.getCategories()
      categories.value = response.data
      return response
    } catch (error) {
      console.error('获取分类失败:', error)
      throw error
    }
  }
  
  const setFilter = (key, value) => {
    filters.value[key] = value
    // 重新获取数据
    fetchTodos(true)
  }
  
  const clearFilters = () => {
    filters.value = {
      status: 'all',
      category: 'all',
      priority: 'all',
      search: ''
    }
    fetchTodos(true)
  }
  
  const refreshTodos = () => {
    return fetchTodos(true)
  }
  
  return {
    // 状态
    todos,
    currentTodo,
    loading,
    categories,
    filters,
    pagination,
    
    // 计算属性
    filteredTodos,
    todoStats,
    urgentTodos,
    
    // 方法
    fetchTodos,
    loadMoreTodos,
    getTodoById,
    createTodo,
    updateTodo,
    deleteTodo,
    toggleTodoStatus,
    fetchCategories,
    setFilter,
    clearFilters,
    refreshTodos,
    isOverdue
  }
})
