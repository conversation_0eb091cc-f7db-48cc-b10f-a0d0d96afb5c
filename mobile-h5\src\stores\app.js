import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const globalLoading = ref(false)
  const theme = ref('light')
  const locale = ref('zh-CN')
  const networkStatus = ref(navigator.onLine)
  const appVersion = ref('1.0.0')
  const isFirstVisit = ref(true)
  const tabBarVisible = ref(true)
  
  // 系统信息
  const systemInfo = ref({
    platform: '',
    userAgent: navigator.userAgent,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    pixelRatio: window.devicePixelRatio
  })
  
  // 计算属性
  const isDarkMode = computed(() => theme.value === 'dark')
  const isOnline = computed(() => networkStatus.value)
  const isMobile = computed(() => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  })
  
  // 方法
  const setGlobalLoading = (loading) => {
    globalLoading.value = loading
  }
  
  const setTheme = (newTheme) => {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
    
    // 更新状态栏颜色
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.content = newTheme === 'dark' ? '#1f1f1f' : '#1989fa'
    }
  }
  
  const setLocale = (newLocale) => {
    locale.value = newLocale
    localStorage.setItem('locale', newLocale)
  }
  
  const setNetworkStatus = (status) => {
    networkStatus.value = status
  }
  
  const setTabBarVisible = (visible) => {
    tabBarVisible.value = visible
  }
  
  const initApp = async () => {
    try {
      // 检测系统信息
      detectSystemInfo()
      
      // 设置主题
      const savedTheme = localStorage.getItem('theme') || 'light'
      setTheme(savedTheme)
      
      // 设置语言
      const savedLocale = localStorage.getItem('locale') || 'zh-CN'
      setLocale(savedLocale)
      
      // 检查是否首次访问
      const hasVisited = localStorage.getItem('hasVisited')
      if (hasVisited) {
        isFirstVisit.value = false
      } else {
        localStorage.setItem('hasVisited', 'true')
      }
      
      // 监听网络状态
      window.addEventListener('online', () => setNetworkStatus(true))
      window.addEventListener('offline', () => setNetworkStatus(false))
      
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }
  
  const detectSystemInfo = () => {
    const ua = navigator.userAgent
    let platform = 'unknown'
    
    if (/iPhone|iPad|iPod/i.test(ua)) {
      platform = 'ios'
    } else if (/Android/i.test(ua)) {
      platform = 'android'
    } else if (/Windows/i.test(ua)) {
      platform = 'windows'
    } else if (/Mac/i.test(ua)) {
      platform = 'mac'
    }
    
    systemInfo.value.platform = platform
  }
  
  const showToast = (message, type = 'success') => {
    // 这里可以集成 Toast 组件
    console.log(`Toast [${type}]: ${message}`)
  }
  
  const showDialog = (options) => {
    // 这里可以集成 Dialog 组件
    console.log('Dialog:', options)
    return Promise.resolve()
  }
  
  const showActionSheet = (options) => {
    // 这里可以集成 ActionSheet 组件
    console.log('ActionSheet:', options)
    return Promise.resolve()
  }
  
  // 重置应用状态
  const resetApp = () => {
    globalLoading.value = false
    theme.value = 'light'
    locale.value = 'zh-CN'
    tabBarVisible.value = true
    
    // 清除本地存储
    localStorage.removeItem('theme')
    localStorage.removeItem('locale')
  }
  
  return {
    // 状态
    globalLoading,
    theme,
    locale,
    networkStatus,
    appVersion,
    isFirstVisit,
    tabBarVisible,
    systemInfo,
    
    // 计算属性
    isDarkMode,
    isOnline,
    isMobile,
    
    // 方法
    setGlobalLoading,
    setTheme,
    setLocale,
    setNetworkStatus,
    setTabBarVisible,
    initApp,
    detectSystemInfo,
    showToast,
    showDialog,
    showActionSheet,
    resetApp
  }
})
