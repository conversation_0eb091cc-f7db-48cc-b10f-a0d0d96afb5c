# Python工具集

这个目录包含了入学待办事项系统的Python工具集，用于数据分析、系统监控、数据迁移等功能。

## 📋 工具列表

### 1. 数据分析工具 (data_analysis.py)

**功能**：
- 用户活跃度分析
- 待办事项完成情况分析
- 性能趋势分析
- 生成可视化图表和报告

**使用方法**：
```bash
# 基本使用
python data_analysis.py --password your_db_password

# 指定数据库配置
python data_analysis.py \
  --host localhost \
  --port 3306 \
  --user todo_user \
  --password your_password \
  --database school_enrollment_todo
```

**输出**：
- 分析图表 (PNG格式)
- JSON格式的详细报告
- 控制台输出的摘要信息

### 2. 数据迁移工具 (data_migration.py)

**功能**：
- 从旧系统迁移用户数据
- 迁移待办事项数据
- 创建待办模板
- 生成迁移报告

**使用方法**：
```bash
# 从旧系统迁移到新系统
python data_migration.py \
  --source-host old_db_host \
  --source-user old_db_user \
  --source-password old_db_password \
  --source-database old_database \
  --target-password new_db_password
```

**迁移步骤**：
1. 创建默认管理员用户
2. 迁移用户数据
3. 创建待办模板
4. 迁移待办事项数据

### 3. 系统监控工具 (system_monitor.py)

**功能**：
- 系统资源监控 (CPU、内存、磁盘)
- 数据库连接状态监控
- API端点健康检查
- 错误日志分析
- 自动告警功能

**使用方法**：
```bash
# 持续监控 (每60秒检查一次)
python system_monitor.py --config monitor_config.json

# 只运行一次检查
python system_monitor.py --once

# 自定义监控间隔
python system_monitor.py --interval 30
```

**配置文件示例** (monitor_config.json)：
```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "user": "todo_user",
    "password": "your_password",
    "database": "school_enrollment_todo"
  },
  "smartcampus": {
    "host": "**********",
    "port": 3306,
    "user": "ethanchen",
    "password": "Spal#2@25",
    "database": "smartcampus"
  },
  "api": {
    "base_url": "http://localhost:3001"
  },
  "logs": {
    "error_log_path": "./logs/error.log"
  },
  "thresholds": {
    "cpu_percent": 80,
    "memory_percent": 85,
    "disk_percent": 90,
    "load_avg": 2.0,
    "api_response_time": 1000,
    "error_count_per_hour": 10
  },
  "email": {
    "smtp_host": "smtp.example.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "email_password",
    "from": "<EMAIL>",
    "to": ["<EMAIL>"]
  }
}
```

## 🚀 安装和配置

### 1. 安装Python依赖

```bash
# 创建虚拟环境 (推荐)
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置数据库连接

确保你有以下数据库的访问权限：
- 待办系统数据库 (MySQL)
- Smartcampus数据库 (MySQL)

### 3. 配置监控

复制并修改监控配置文件：
```bash
cp monitor_config.example.json monitor_config.json
# 编辑配置文件，填入正确的数据库和邮件配置
```

## 📊 数据分析功能详解

### 用户活跃度分析
- 总用户数和活跃用户数
- 用户角色分布
- 语言偏好分布
- 每日注册趋势
- 周活跃用户统计

### 待办事项分析
- 总体完成率
- 各状态分布 (待处理、进行中、已完成、已取消)
- 各分类完成情况
- 优先级分布
- 平均完成时间

### 性能趋势分析
- 每日完成数量趋势
- 每日创建数量趋势
- 平均完成时间变化

### 可视化图表
- 用户角色分布饼图
- 待办状态分布柱状图
- 分类完成率对比
- 优先级分布
- 语言偏好饼图
- 每日完成趋势线图

## 🔧 系统监控功能详解

### 系统资源监控
- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络IO统计
- 系统负载
- 进程数量

### 数据库监控
- 连接状态检查
- 响应时间测量
- 连接失败告警

### API监控
- 健康检查端点
- 认证端点测试
- 业务端点测试
- 响应时间监控

### 日志分析
- 错误日志统计
- 警告日志统计
- 最近错误记录

### 告警功能
- 阈值检查
- 邮件告警
- 日志记录
- 历史告警查询

## 📈 使用场景

### 日常运维
```bash
# 每天运行数据分析
python data_analysis.py --password your_password

# 启动系统监控
python system_monitor.py --config monitor_config.json
```

### 系统迁移
```bash
# 从旧系统迁移数据
python data_migration.py \
  --source-host old_server \
  --source-user old_user \
  --source-password old_password \
  --source-database old_db \
  --target-password new_password
```

### 故障排查
```bash
# 快速系统检查
python system_monitor.py --once

# 分析最近的系统性能
python data_analysis.py --password your_password
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认网络连通性

2. **依赖安装失败**
   - 更新pip: `pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

3. **图表显示问题**
   - 安装中文字体
   - 检查matplotlib配置
   - 确认显示环境支持GUI

4. **邮件告警不工作**
   - 检查SMTP配置
   - 验证邮箱密码
   - 确认防火墙设置

### 日志文件
- `data_analysis.log` - 数据分析日志
- `data_migration.log` - 数据迁移日志
- `system_monitor.log` - 系统监控日志

## 📝 开发指南

### 添加新的分析功能
1. 在 `data_analysis.py` 中添加新的分析方法
2. 更新 `run_full_analysis()` 方法
3. 添加相应的可视化代码

### 添加新的监控指标
1. 在 `system_monitor.py` 中添加新的检查方法
2. 更新 `collect_metrics()` 方法
3. 配置相应的阈值

### 扩展迁移功能
1. 在 `data_migration.py` 中添加新的迁移方法
2. 更新 `run_migration()` 方法
3. 添加数据验证逻辑

## 📄 许可证

本工具集采用 MIT 许可证，详见项目根目录的 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这些工具！

---

**维护者**: Todo System Team  
**最后更新**: 2024-12-05
