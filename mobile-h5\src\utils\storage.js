// Token 相关
const TOKEN_KEY = 'token'
const REFRESH_TOKEN_KEY = 'refresh_token'

export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
}

export function getRefreshToken() {
  return localStorage.getItem(REFRESH_TOKEN_KEY)
}

export function setRefreshToken(token) {
  localStorage.setItem(REFRESH_TOKEN_KEY, token)
}

// 用户信息相关
const USER_INFO_KEY = 'user_info'

export function getUserInfo() {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

export function setUserInfo(userInfo) {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

export function removeUserInfo() {
  localStorage.removeItem(USER_INFO_KEY)
}

// 应用设置相关
const SETTINGS_KEY = 'app_settings'

export function getSettings() {
  const settings = localStorage.getItem(SETTINGS_KEY)
  return settings ? JSON.parse(settings) : {}
}

export function setSetting(key, value) {
  const settings = getSettings()
  settings[key] = value
  localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings))
}

export function getSetting(key, defaultValue = null) {
  const settings = getSettings()
  return settings[key] !== undefined ? settings[key] : defaultValue
}

export function removeSettings() {
  localStorage.removeItem(SETTINGS_KEY)
}

// 缓存相关
export function setCache(key, data, expireTime = 0) {
  const cacheData = {
    data,
    timestamp: Date.now(),
    expireTime
  }
  localStorage.setItem(`cache_${key}`, JSON.stringify(cacheData))
}

export function getCache(key) {
  const cacheStr = localStorage.getItem(`cache_${key}`)
  if (!cacheStr) return null
  
  try {
    const cache = JSON.parse(cacheStr)
    const now = Date.now()
    
    // 检查是否过期
    if (cache.expireTime > 0 && now - cache.timestamp > cache.expireTime) {
      removeCache(key)
      return null
    }
    
    return cache.data
  } catch (error) {
    console.error('解析缓存数据失败:', error)
    removeCache(key)
    return null
  }
}

export function removeCache(key) {
  localStorage.removeItem(`cache_${key}`)
}

export function clearCache() {
  const keys = Object.keys(localStorage)
  keys.forEach(key => {
    if (key.startsWith('cache_')) {
      localStorage.removeItem(key)
    }
  })
}

// SessionStorage 相关
export function setSessionData(key, data) {
  sessionStorage.setItem(key, JSON.stringify(data))
}

export function getSessionData(key) {
  const data = sessionStorage.getItem(key)
  return data ? JSON.parse(data) : null
}

export function removeSessionData(key) {
  sessionStorage.removeItem(key)
}

export function clearSessionData() {
  sessionStorage.clear()
}

// 清除所有存储数据
export function clearAllStorage() {
  localStorage.clear()
  sessionStorage.clear()
}

// 存储大小计算
export function getStorageSize() {
  let localStorageSize = 0
  let sessionStorageSize = 0
  
  // 计算 localStorage 大小
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      localStorageSize += localStorage[key].length + key.length
    }
  }
  
  // 计算 sessionStorage 大小
  for (let key in sessionStorage) {
    if (sessionStorage.hasOwnProperty(key)) {
      sessionStorageSize += sessionStorage[key].length + key.length
    }
  }
  
  return {
    localStorage: (localStorageSize / 1024).toFixed(2) + ' KB',
    sessionStorage: (sessionStorageSize / 1024).toFixed(2) + ' KB',
    total: ((localStorageSize + sessionStorageSize) / 1024).toFixed(2) + ' KB'
  }
}

// 检查存储是否可用
export function isStorageAvailable(type = 'localStorage') {
  try {
    const storage = window[type]
    const testKey = '__storage_test__'
    storage.setItem(testKey, 'test')
    storage.removeItem(testKey)
    return true
  } catch (error) {
    return false
  }
}
