// 导入变量和混合器
@import './variables.scss';
@import './mixins.scss';

// 重置样式
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-size-md);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  overflow-x: hidden;
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
  
  &:active {
    opacity: 0.7;
  }
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 表单元素样式
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  
  &:disabled {
    cursor: not-allowed;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-muted {
  color: var(--text-tertiary);
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-success {
  background-color: var(--success-color);
}

.bg-warning {
  background-color: var(--warning-color);
}

.bg-danger {
  background-color: var(--danger-color);
}

.bg-white {
  background-color: var(--bg-primary);
}

.bg-gray {
  background-color: var(--bg-secondary);
}

// 间距工具类
@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in (0: 0, 1: var(--spacing-xs), 2: var(--spacing-sm), 3: var(--spacing-md), 4: var(--spacing-lg), 5: var(--spacing-xl)) {
    .#{$abbrev}-#{$size} { #{$prop}: $length; }
    .#{$abbrev}t-#{$size} { #{$prop}-top: $length; }
    .#{$abbrev}r-#{$size} { #{$prop}-right: $length; }
    .#{$abbrev}b-#{$size} { #{$prop}-bottom: $length; }
    .#{$abbrev}l-#{$size} { #{$prop}-left: $length; }
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length;
      #{$prop}-right: $length;
    }
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length;
      #{$prop}-bottom: $length;
    }
  }
}

// Flex 工具类
.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.align-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

// 显示/隐藏工具类
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

// 位置工具类
.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.position-sticky {
  position: sticky;
}

// 圆角工具类
.rounded {
  border-radius: var(--border-radius-md);
}

.rounded-sm {
  border-radius: var(--border-radius-sm);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.rounded-xl {
  border-radius: var(--border-radius-xl);
}

.rounded-circle {
  border-radius: 50%;
}

// 阴影工具类
.shadow-sm {
  box-shadow: var(--shadow-light);
}

.shadow {
  box-shadow: var(--shadow-medium);
}

.shadow-lg {
  box-shadow: var(--shadow-heavy);
}

.shadow-none {
  box-shadow: none;
}

// 文本工具类
.text-ellipsis {
  @include text-ellipsis(1);
}

.text-ellipsis-2 {
  @include text-ellipsis(2);
}

.text-ellipsis-3 {
  @include text-ellipsis(3);
}

.font-weight-light {
  font-weight: var(--font-weight-light);
}

.font-weight-normal {
  font-weight: var(--font-weight-normal);
}

.font-weight-medium {
  font-weight: var(--font-weight-medium);
}

.font-weight-bold {
  font-weight: var(--font-weight-bold);
}

.font-size-xs {
  font-size: var(--font-size-xs);
}

.font-size-sm {
  font-size: var(--font-size-sm);
}

.font-size-md {
  font-size: var(--font-size-md);
}

.font-size-lg {
  font-size: var(--font-size-lg);
}

.font-size-xl {
  font-size: var(--font-size-xl);
}

// 宽度工具类
.w-25 {
  width: 25%;
}

.w-50 {
  width: 50%;
}

.w-75 {
  width: 75%;
}

.w-100 {
  width: 100%;
}

.w-auto {
  width: auto;
}

// 高度工具类
.h-25 {
  height: 25%;
}

.h-50 {
  height: 50%;
}

.h-75 {
  height: 75%;
}

.h-100 {
  height: 100%;
}

.h-auto {
  height: auto;
}

// 安全区域适配
.safe-area-top {
  @include safe-area-inset(padding-top, top);
}

.safe-area-bottom {
  @include safe-area-inset(padding-bottom, bottom);
}

.safe-area-left {
  @include safe-area-inset(padding-left, left);
}

.safe-area-right {
  @include safe-area-inset(padding-right, right);
}
