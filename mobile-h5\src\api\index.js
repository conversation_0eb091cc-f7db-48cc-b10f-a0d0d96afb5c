import axios from 'axios'
import { getToken, removeToken } from '../utils/storage'
import { useAppStore } from '../stores/app'
import { useUserStore } from '../stores/user'
import router from '../router'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const appStore = useAppStore()
    
    // 添加token
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()
    
    // 显示全局加载
    if (config.showLoading !== false) {
      appStore.setGlobalLoading(true)
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const appStore = useAppStore()
    
    // 隐藏全局加载
    appStore.setGlobalLoading(false)
    
    console.log('API响应:', response.config.url, response.data)
    
    // 检查业务状态码
    if (response.data.code !== undefined && response.data.code !== 200) {
      const error = new Error(response.data.message || '请求失败')
      error.code = response.data.code
      error.response = response
      return Promise.reject(error)
    }
    
    return response.data
  },
  async (error) => {
    const appStore = useAppStore()
    const userStore = useUserStore()
    
    // 隐藏全局加载
    appStore.setGlobalLoading(false)
    
    console.error('API错误:', error.config?.url, error.response?.data || error.message)
    
    // 处理不同的错误状态
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          removeToken()
          userStore.logout()
          
          if (router.currentRoute.value.path !== '/login') {
            appStore.showToast('登录已过期，请重新登录', 'error')
            router.push('/login')
          }
          break
          
        case 403:
          appStore.showToast('没有权限访问该资源', 'error')
          break
          
        case 404:
          appStore.showToast('请求的资源不存在', 'error')
          break
          
        case 422:
          // 表单验证错误
          if (data.errors) {
            const firstError = Object.values(data.errors)[0]
            appStore.showToast(Array.isArray(firstError) ? firstError[0] : firstError, 'error')
          } else {
            appStore.showToast(data.message || '请求参数错误', 'error')
          }
          break
          
        case 429:
          appStore.showToast('请求过于频繁，请稍后再试', 'error')
          break
          
        case 500:
          appStore.showToast('服务器内部错误', 'error')
          break
          
        default:
          appStore.showToast(data?.message || '网络请求失败', 'error')
      }
    } else if (error.code === 'ECONNABORTED') {
      appStore.showToast('请求超时，请检查网络连接', 'error')
    } else if (error.message === 'Network Error') {
      appStore.showToast('网络连接失败，请检查网络设置', 'error')
    } else {
      appStore.showToast(error.message || '未知错误', 'error')
    }
    
    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId() {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 请求方法封装
export const api = {
  get(url, params = {}, config = {}) {
    return request({
      method: 'GET',
      url,
      params,
      ...config
    })
  },
  
  post(url, data = {}, config = {}) {
    return request({
      method: 'POST',
      url,
      data,
      ...config
    })
  },
  
  put(url, data = {}, config = {}) {
    return request({
      method: 'PUT',
      url,
      data,
      ...config
    })
  },
  
  patch(url, data = {}, config = {}) {
    return request({
      method: 'PATCH',
      url,
      data,
      ...config
    })
  },
  
  delete(url, config = {}) {
    return request({
      method: 'DELETE',
      url,
      ...config
    })
  },
  
  upload(url, formData, config = {}) {
    return request({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  }
}

// 导出axios实例
export default request
