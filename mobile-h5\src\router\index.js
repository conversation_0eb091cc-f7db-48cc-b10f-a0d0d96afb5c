import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'

// 路由懒加载
const Login = () => import('../views/auth/Login.vue')
const Register = () => import('../views/auth/Register.vue')
const ForgotPassword = () => import('../views/auth/ForgotPassword.vue')
const Home = () => import('../views/home/<USER>')
const TodoList = () => import('../views/todo/TodoList.vue')
const TodoDetail = () => import('../views/todo/TodoDetail.vue')
const Profile = () => import('../views/profile/Profile.vue')
const Settings = () => import('../views/profile/Settings.vue')
const NotFound = () => import('../views/NotFound.vue')

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false,
      hideTabBar: true,
      transition: 'fade'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '注册',
      requiresAuth: false,
      hideTabBar: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    meta: {
      title: '忘记密码',
      requiresAuth: false,
      hideTabBar: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      requiresAuth: true,
      keepAlive: true,
      transition: 'fade'
    }
  },
  {
    path: '/todo',
    name: 'TodoList',
    component: TodoList,
    meta: {
      title: '待办事项',
      requiresAuth: true,
      keepAlive: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/todo/:id',
    name: 'TodoDetail',
    component: TodoDetail,
    meta: {
      title: '待办详情',
      requiresAuth: true,
      hideTabBar: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心',
      requiresAuth: true,
      keepAlive: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '设置',
      requiresAuth: true,
      hideTabBar: true,
      transition: 'slide-left'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面不存在',
      requiresAuth: false,
      hideTabBar: true,
      transition: 'fade'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 保存目标路由，登录后跳转
      sessionStorage.setItem('redirectPath', to.fullPath)
      next('/login')
      return
    }
    
    // 检查token是否有效
    if (userStore.token && !userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        userStore.logout()
        next('/login')
        return
      }
    }
  }
  
  // 已登录用户访问登录页面，重定向到首页
  if (to.name === 'Login' && userStore.isLoggedIn) {
    next('/home')
    return
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 页面切换完成后的处理
  console.log(`路由切换: ${from.path} -> ${to.path}`)
})

export default router
