const xss = require('xss');
const logger = require('../utils/logger');

/**
 * 安全中间件
 * 提供各种安全防护功能
 */

/**
 * XSS防护中间件
 * 清理请求中的恶意脚本
 */
const xssProtection = (req, res, next) => {
  try {
    // 清理请求体
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    // 清理查询参数
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }

    // 清理URL参数
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }

    next();
  } catch (error) {
    logger.error('XSS protection middleware error', {
      error: error.message,
      url: req.originalUrl
    });
    next();
  }
};

/**
 * 递归清理对象中的XSS
 * @param {*} obj - 要清理的对象
 * @returns {*} 清理后的对象
 */
function sanitizeObject(obj) {
  if (typeof obj === 'string') {
    return xss(obj, {
      whiteList: {
        a: ['href', 'title'],
        b: [],
        i: [],
        strong: [],
        em: [],
        p: [],
        br: []
      },
      stripIgnoreTag: true,
      stripIgnoreTagBody: ['script']
    });
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  if (typeof obj === 'object' && obj !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }

  return obj;
}

/**
 * SQL注入防护中间件
 * 检测和阻止SQL注入尝试
 */
const sqlInjectionProtection = (req, res, next) => {
  const sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(\b(OR|AND)\s+['"]\w+['"]?\s*=\s*['"]\w+['"]?)/i,
    /(--|\/\*|\*\/|;)/,
    /(\b(EXEC|EXECUTE)\s*\()/i,
    /(\b(SP_|XP_)\w+)/i
  ];

  const checkForSqlInjection = (value) => {
    if (typeof value === 'string') {
      return sqlInjectionPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj, path = '') => {
    if (typeof obj === 'string') {
      if (checkForSqlInjection(obj)) {
        logger.security('SQL injection attempt detected', {
          value: obj,
          path,
          ip: req.ip,
          url: req.originalUrl,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString()
        });
        return true;
      }
    } else if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        if (checkObject(obj[i], `${path}[${i}]`)) {
          return true;
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (checkObject(value, path ? `${path}.${key}` : key)) {
          return true;
        }
      }
    }
    return false;
  };

  try {
    // 检查请求体
    if (req.body && checkObject(req.body, 'body')) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求包含非法字符',
        timestamp: new Date().toISOString()
      });
    }

    // 检查查询参数
    if (req.query && checkObject(req.query, 'query')) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求包含非法字符',
        timestamp: new Date().toISOString()
      });
    }

    // 检查URL参数
    if (req.params && checkObject(req.params, 'params')) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求包含非法字符',
        timestamp: new Date().toISOString()
      });
    }

    next();
  } catch (error) {
    logger.error('SQL injection protection middleware error', {
      error: error.message,
      url: req.originalUrl
    });
    next();
  }
};

/**
 * 路径遍历防护中间件
 * 防止目录遍历攻击
 */
const pathTraversalProtection = (req, res, next) => {
  const pathTraversalPatterns = [
    /\.\.\//,
    /\.\.\\/,
    /%2e%2e%2f/i,
    /%2e%2e%5c/i,
    /\.\.%2f/i,
    /\.\.%5c/i
  ];

  const checkForPathTraversal = (value) => {
    if (typeof value === 'string') {
      return pathTraversalPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj) => {
    if (typeof obj === 'string') {
      return checkForPathTraversal(obj);
    } else if (Array.isArray(obj)) {
      return obj.some(checkObject);
    } else if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(checkObject);
    }
    return false;
  };

  try {
    // 检查URL
    if (checkForPathTraversal(req.originalUrl)) {
      logger.security('Path traversal attempt detected in URL', {
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      return res.status(400).json({
        success: false,
        code: 4010,
        message: '非法的请求路径',
        timestamp: new Date().toISOString()
      });
    }

    // 检查请求参数
    if ((req.body && checkObject(req.body)) ||
        (req.query && checkObject(req.query)) ||
        (req.params && checkObject(req.params))) {
      
      logger.security('Path traversal attempt detected in parameters', {
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      return res.status(400).json({
        success: false,
        code: 4010,
        message: '请求参数包含非法字符',
        timestamp: new Date().toISOString()
      });
    }

    next();
  } catch (error) {
    logger.error('Path traversal protection middleware error', {
      error: error.message,
      url: req.originalUrl
    });
    next();
  }
};

/**
 * 请求大小限制中间件
 * 防止大文件攻击
 */
const requestSizeLimit = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const maxSizeBytes = parseSize(maxSize);

    if (contentLength > maxSizeBytes) {
      logger.security('Request size limit exceeded', {
        contentLength,
        maxSize: maxSizeBytes,
        ip: req.ip,
        url: req.originalUrl,
        timestamp: new Date().toISOString()
      });

      return res.status(413).json({
        success: false,
        code: 4013,
        message: '请求体过大',
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * 解析大小字符串为字节数
 * @param {string} size - 大小字符串 (如 '10mb', '1gb')
 * @returns {number} 字节数
 */
function parseSize(size) {
  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };

  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)(b|kb|mb|gb)$/);
  if (!match) {
    return 10 * 1024 * 1024; // 默认10MB
  }

  const [, value, unit] = match;
  return parseFloat(value) * (units[unit] || 1);
}

/**
 * 内容类型验证中间件
 * 验证请求的内容类型
 */
const contentTypeValidation = (allowedTypes = ['application/json']) => {
  return (req, res, next) => {
    // 跳过GET请求和没有请求体的请求
    if (req.method === 'GET' || !req.get('Content-Length') || req.get('Content-Length') === '0') {
      return next();
    }

    const contentType = req.get('Content-Type');
    if (!contentType) {
      return res.status(400).json({
        success: false,
        code: 4010,
        message: '缺少Content-Type头',
        timestamp: new Date().toISOString()
      });
    }

    const isAllowed = allowedTypes.some(type => contentType.includes(type));
    if (!isAllowed) {
      logger.security('Invalid content type', {
        contentType,
        allowedTypes,
        ip: req.ip,
        url: req.originalUrl,
        timestamp: new Date().toISOString()
      });

      return res.status(415).json({
        success: false,
        code: 4015,
        message: '不支持的内容类型',
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * 组合安全中间件
 */
const securityMiddleware = [
  xssProtection,
  sqlInjectionProtection,
  pathTraversalProtection,
  requestSizeLimit('10mb'),
  contentTypeValidation(['application/json', 'multipart/form-data', 'application/x-www-form-urlencoded'])
];

module.exports = {
  xssProtection,
  sqlInjectionProtection,
  pathTraversalProtection,
  requestSizeLimit,
  contentTypeValidation,
  securityMiddleware
};
