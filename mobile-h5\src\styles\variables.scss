// 颜色变量
:root {
  // 主题色
  --primary-color: #1989fa;
  --primary-light: #4dabf7;
  --primary-dark: #1c7ed6;
  
  // 辅助色
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --info-color: #1989fa;
  
  // 中性色
  --text-primary: #323233;
  --text-secondary: #646566;
  --text-tertiary: #969799;
  --text-disabled: #c8c9cc;
  
  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f7f8fa;
  --bg-tertiary: #ebedf0;
  --bg-disabled: #f5f5f5;
  
  // 边框色
  --border-color: #ebedf0;
  --border-light: #f2f3f5;
  --border-dark: #dcdee0;
  
  // 阴影
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
  
  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  // 字体大小
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;
  --font-size-xxxl: 24px;
  
  // 行高
  --line-height-xs: 1.2;
  --line-height-sm: 1.4;
  --line-height-md: 1.5;
  --line-height-lg: 1.6;
  --line-height-xl: 1.8;
  
  // 字重
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  --font-weight-heavy: 700;
  
  // Z-index
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
  
  // 动画时间
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  // 动画函数
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
}

// 暗色主题
[data-theme="dark"] {
  // 主题色保持不变
  --primary-color: #1989fa;
  --primary-light: #4dabf7;
  --primary-dark: #1c7ed6;
  
  // 辅助色
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --info-color: #1989fa;
  
  // 中性色 - 暗色主题调整
  --text-primary: #ffffff;
  --text-secondary: #e5e5e5;
  --text-tertiary: #b3b3b3;
  --text-disabled: #666666;
  
  // 背景色 - 暗色主题调整
  --bg-primary: #1f1f1f;
  --bg-secondary: #2a2a2a;
  --bg-tertiary: #3a3a3a;
  --bg-disabled: #4a4a4a;
  
  // 边框色 - 暗色主题调整
  --border-color: #3a3a3a;
  --border-light: #2a2a2a;
  --border-dark: #4a4a4a;
}

// SCSS 变量 (用于编译时)
$primary-color: #1989fa;
$success-color: #07c160;
$warning-color: #ff976a;
$danger-color: #ee0a24;
$info-color: #1989fa;

$text-primary: #323233;
$text-secondary: #646566;
$text-tertiary: #969799;

$bg-primary: #ffffff;
$bg-secondary: #f7f8fa;
$bg-tertiary: #ebedf0;

$border-color: #ebedf0;

$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;

$transition-fast: 0.2s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

// 断点
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// 容器最大宽度
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);
