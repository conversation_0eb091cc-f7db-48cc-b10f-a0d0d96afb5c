<template>
  <div id="app" class="app">
    <!-- 网络状态提示 -->
    <van-notice-bar
      v-if="!isOnline"
      left-icon="warning-o"
      text="网络连接已断开，请检查网络设置"
      color="#ed6a0c"
      background="#fffbe8"
      :scrollable="false"
    />
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="route.meta.transition || 'slide-left'"
        mode="out-in"
      >
        <keep-alive :include="route.meta.keepAlive ? [route.name] : []">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </transition>
    </router-view>
    
    <!-- 全局加载提示 -->
    <van-overlay :show="globalLoading" class="loading-overlay">
      <div class="loading-content">
        <van-loading size="24px" color="#1989fa">加载中...</van-loading>
      </div>
    </van-overlay>
    
    <!-- Toast 容器 -->
    <van-toast />
    
    <!-- Dialog 容器 -->
    <van-dialog />
    
    <!-- ActionSheet 容器 -->
    <van-action-sheet />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useAppStore } from './stores/app'
import { useUserStore } from './stores/user'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

const { globalLoading } = storeToRefs(appStore)
const isOnline = ref(navigator.onLine)

// 网络状态监听
const handleOnline = () => {
  isOnline.value = true
}

const handleOffline = () => {
  isOnline.value = false
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // 页面变为可见时，刷新用户状态
    if (userStore.isLoggedIn) {
      userStore.refreshUserInfo()
    }
  }
}

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 入学待办事项系统`
  }
  
  // 检查登录状态
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  next()
})

onMounted(() => {
  // 监听网络状态
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 初始化应用
  appStore.initApp()
  
  // 自动登录检查
  if (userStore.token) {
    userStore.checkLoginStatus()
  }
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})
</script>

<style lang="scss">
@use './styles/variables.scss' as *;
@use './styles/mixins.scss' as *;
@use './styles/index.scss' as *;

.app {
  min-height: 100vh;
  background-color: var(--van-background-color);
}

// 页面过渡动画
.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
}

.slide-left-leave-to {
  transform: translateX(-100%);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: transform 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
}

.slide-right-leave-to {
  transform: translateX(100%);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 全局加载遮罩
.loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
  }
}
</style>
