/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./src/components/common/AppFooter.vue')['default']
    AppHeader: typeof import('./src/components/common/AppHeader.vue')['default']
    Loading: typeof import('./src/components/common/Loading.vue')['default']
    ProgressBar: typeof import('./src/components/business/ProgressBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TodoCard: typeof import('./src/components/business/TodoCard.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanDialog: typeof import('vant/es')['Dialog']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanFloatingBubble: typeof import('vant/es')['FloatingBubble']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPopup: typeof import('vant/es')['Popup']
    VanProgress: typeof import('vant/es')['Progress']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanSearch: typeof import('vant/es')['Search']
    VanStep: typeof import('vant/es')['Step']
    VanSteps: typeof import('vant/es')['Steps']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanToast: typeof import('vant/es')['Toast']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
