/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./src/components/common/AppFooter.vue')['default']
    AppHeader: typeof import('./src/components/common/AppHeader.vue')['default']
    Loading: typeof import('./src/components/common/Loading.vue')['default']
    ProgressBar: typeof import('./src/components/business/ProgressBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TodoCard: typeof import('./src/components/business/TodoCard.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanButton: typeof import('vant/es')['Button']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanDialog: typeof import('vant/es')['Dialog']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanStep: typeof import('vant/es')['Step']
    VanSteps: typeof import('vant/es')['Steps']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanToast: typeof import('vant/es')['Toast']
  }
}
